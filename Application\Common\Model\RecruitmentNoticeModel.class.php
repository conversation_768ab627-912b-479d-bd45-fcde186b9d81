<?php
namespace Common\Model;

use Think\Model;

/**
 * 招聘公告模型
 */
class RecruitmentNoticeModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 状态定义
     */
    public $status = [
        '0' => ['text' => '停用', 'style' => 'danger'],
        '1' => ['text' => '启用', 'style' => 'success'],
    ];

    /**
     * 获取招聘公告列表（带关联岗位信息）
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getNoticeList($where = [], $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $list = $this->where($where)
            ->order('id DESC')
            ->limit($offset, $limit)
            ->select();

        if ($list) {
            // 获取关联的岗位信息
            $noticeIds = array_column($list, 'id');
            $postRelations = D('RecruitmentNoticePost')
                ->alias('rnp')
                ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
                ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
                ->where(['rnp.notice_id' => ['in', $noticeIds]])
                ->field('rnp.notice_id, pp.id as post_id, pp.job_name, p.name as project_name')
                ->select();

            // 按公告ID分组岗位信息
            $postsByNotice = [];
            foreach ($postRelations as $relation) {
                $postsByNotice[$relation['notice_id']][] = $relation;
            }

            // 将岗位信息添加到公告列表中
            foreach ($list as &$notice) {
                $notice['posts'] = isset($postsByNotice[$notice['id']]) ? $postsByNotice[$notice['id']] : [];
                $notice['post_count'] = count($notice['posts']);
            }
        }

        return $list;
    }

    /**
     * 获取招聘公告详情（包含关联岗位和要求）
     * @param int $id 公告ID
     * @return array|false
     */
    public function getNoticeDetail($id)
    {
        $notice = $this->find($id);
        if (!$notice) {
            return false;
        }

        // 获取关联的岗位信息
        $posts = D('RecruitmentNoticePost')
            ->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $id])
            ->field('pp.*, p.name as project_name, rnp.id as relation_id')
            ->select();

        // 获取每个岗位的筛选要求
        if ($posts) {
            $postIds = array_column($posts, 'id');
            $requirements = D('PostRequirements')
                ->where(['post_id' => ['in', $postIds], 'notice_id' => $id])
                ->select();

            // 按岗位ID分组要求
            $requirementsByPost = [];
            foreach ($requirements as $req) {
                $requirementsByPost[$req['post_id']] = $req;
            }

            // 将要求信息添加到岗位中
            foreach ($posts as &$post) {
                $post['requirements'] = isset($requirementsByPost[$post['id']]) ? $requirementsByPost[$post['id']] : null;
            }
        }

        $notice['posts'] = $posts ?: [];
        return $notice;
    }

    /**
     * 删除招聘公告（级联删除相关数据）
     * @param int $id 公告ID
     * @return bool
     */
    public function deleteNotice($id)
    {
        $this->startTrans();
        try {
            // 删除岗位关联
            D('RecruitmentNoticePost')->where(['notice_id' => $id])->delete();
            
            // 删除岗位要求
            D('PostRequirements')->where(['notice_id' => $id])->delete();
            
            // 删除匹配记录
            D('ResumePostMatch')->where(['notice_id' => $id])->delete();
            
            // 删除公告
            $result = $this->delete($id);
            
            if ($result) {
                $this->commit();
                return true;
            } else {
                $this->rollback();
                return false;
            }
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 获取可用的岗位列表（用于关联选择）
     * @return array
     */
    public function getAvailablePosts()
    {
        return D('ProjectPost')
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.status' => 1, 'p.status' => 1])
            ->field('pp.id, pp.job_name, p.name as project_name')
            ->order('p.id ASC, pp.id ASC')
            ->select();
    }
}
