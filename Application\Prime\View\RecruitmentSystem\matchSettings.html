<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简历匹配设置</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                简历匹配设置
                <small>{$notice.title}</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentsystem/index')}">招聘筛选系统</a></li>
                <li><a href="{:U('recruitmentsystem/matches', array('notice_id'=>$notice['id']))}">匹配结果</a></li>
                <li class="active">匹配设置</li>
            </ol>
        </section>

        <section class="content">
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">执行简历匹配</h3>
                        </div>

                        <form method="post" action="{:U('recruitmentsystem/executeMatch')}" id="matchForm">
                            <input type="hidden" name="notice_id" value="{$notice.id}">
                            
                            <div class="box-body">
                                <!-- 当前状态信息 -->
                                <div class="alert alert-info">
                                    <h4><i class="fa fa-info-circle"></i> 当前状态</h4>
                                    <ul class="list-unstyled">
                                        <li><strong>招聘公告：</strong>{$notice.title}</li>
                                        <li><strong>现有匹配记录：</strong>{$existingCount} 条</li>
                                        <li><strong>可匹配简历：</strong>{$totalResumes} 份（沟通中和培训中的简历）</li>
                                    </ul>
                                </div>

                                <!-- 匹配选项 -->
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="clear_old" value="1" 
                                               <if condition="$existingCount gt 0">checked</if>> 
                                        清除现有匹配记录
                                    </label>
                                    <p class="help-block">
                                        <if condition="$existingCount gt 0">
                                            建议勾选此选项，以确保使用最新的筛选要求重新匹配
                                        <else/>
                                            当前没有匹配记录，可以直接执行匹配
                                        </if>
                                    </p>
                                </div>

                                <!-- 匹配算法说明 -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#algorithmInfo">
                                                匹配算法说明 <i class="fa fa-chevron-down"></i>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="algorithmInfo" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <p>系统将根据以下条件进行匹配，总分100分：</p>
                                            <table class="table table-bordered table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>匹配项目</th>
                                                        <th>权重分数</th>
                                                        <th>条件类型</th>
                                                        <th>说明</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>年龄匹配</td>
                                                        <td>15分</td>
                                                        <td><span class="label label-danger">硬性条件</span></td>
                                                        <td>简历年龄在岗位要求范围内</td>
                                                    </tr>
                                                    <tr>
                                                        <td>性别匹配</td>
                                                        <td>10分</td>
                                                        <td><span class="label label-danger">硬性条件</span></td>
                                                        <td>简历性别符合岗位要求</td>
                                                    </tr>
                                                    <tr>
                                                        <td>身高匹配</td>
                                                        <td>10分</td>
                                                        <td><span class="label label-danger">硬性条件</span></td>
                                                        <td>简历身高在岗位要求范围内</td>
                                                    </tr>
                                                    <tr>
                                                        <td>学历匹配</td>
                                                        <td>25分</td>
                                                        <td><span class="label label-danger">硬性条件</span></td>
                                                        <td>简历学历达到岗位要求，超出要求可加分</td>
                                                    </tr>
                                                    <tr>
                                                        <td>专业匹配</td>
                                                        <td>20分</td>
                                                        <td><span class="label label-info">软性条件</span></td>
                                                        <td>简历专业包含岗位要求的关键词</td>
                                                    </tr>
                                                    <tr>
                                                        <td>工作经验</td>
                                                        <td>15分</td>
                                                        <td><span class="label label-info">软性条件</span></td>
                                                        <td>简历工作年限符合岗位要求</td>
                                                    </tr>
                                                    <tr>
                                                        <td>其他条件</td>
                                                        <td>5分</td>
                                                        <td><span class="label label-info">软性条件</span></td>
                                                        <td>健康状况、恐高等其他要求</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <p class="text-muted">
                                                <small>
                                                    <strong>硬性条件：</strong>不符合将标记为"不符合要求"；<br>
                                                    <strong>软性条件：</strong>不符合不影响"符合要求"状态，但会影响匹配分数。
                                                </small>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 预计处理时间提示 -->
                                <div class="alert alert-warning">
                                    <i class="fa fa-clock-o"></i>
                                    <strong>预计处理时间：</strong>
                                    根据简历数量，匹配过程可能需要几秒到几分钟不等，请耐心等待。
                                </div>
                            </div>

                            <div class="box-footer">
                                <button type="submit" class="btn btn-primary btn-lg" id="executeBtn">
                                    <i class="fa fa-play"></i> 开始匹配
                                </button>
                                <a href="{:U('recruitmentsystem/matches', array('notice_id'=>$notice['id']))}" 
                                   class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> 返回
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

<script>
$(function() {
    // 表单提交处理
    $('#matchForm').submit(function() {
        // 显示加载状态
        $('#executeBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 匹配中...');
        
        return true;
    });
});
</script>

</body>
</html>
