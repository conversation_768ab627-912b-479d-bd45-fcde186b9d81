<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Common\Service\ResumeMatchService;

/**
 * 简历匹配控制器
 */
class ResumeMatchController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 匹配结果列表（按招聘公告查看）
     */
    public function index()
    {
        $noticeId = intval(I('get.notice_id'));
        if (!$noticeId) {
            // 显示招聘公告选择页面
            $this->selectNotice();
            return;
        }

        // 获取招聘公告信息
        $notice = D('RecruitmentNotice')->getNoticeDetail($noticeId);
        if (!$notice) $this->error('招聘公告不存在');

        // 筛选条件
        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
            'post_id' => I('get.post_id', ''),
        ];

        // 分页参数
        $page = intval(I('get.p', 1));
        $limit = 20;

        // 获取匹配结果
        $matchObj = D('ResumePostMatch');
        $list = $matchObj->getNoticeMatches($noticeId, $filters, $page, $limit);

        // 获取总数（用于分页）
        $where = ['notice_id' => $noticeId];
        if ($filters['is_qualified'] !== '') $where['is_qualified'] = $filters['is_qualified'];
        if ($filters['min_score'] !== '') $where['match_score'] = ['egt', $filters['min_score']];
        if ($filters['post_id']) $where['post_id'] = $filters['post_id'];
        
        $count = $matchObj->where($where)->count();
        $pageObj = $this->page($count, $limit);

        // 获取统计信息
        $stats = $matchObj->getMatchStats($noticeId);

        $this->assign('notice', $notice);
        $this->assign('list', $list);
        $this->assign('page', $pageObj->show());
        $this->assign('stats', $stats);
        $this->assign('filters', $filters);
        $this->assign('qualifiedList', $matchObj->is_qualified);
        $this->display();
    }

    /**
     * 选择招聘公告
     */
    private function selectNotice()
    {
        $notices = D('RecruitmentNotice')->where(['status' => 1])->order('id DESC')->select();
        $this->assign('notices', $notices);
        $this->display('select_notice');
    }

    /**
     * 岗位匹配结果详情
     */
    public function postDetail()
    {
        $postId = intval(I('get.post_id'));
        $noticeId = intval(I('get.notice_id'));
        
        if (!$postId || !$noticeId) $this->error('参数错误');

        // 获取岗位信息
        $post = D('ProjectPost')->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $postId])
            ->field('pp.*, p.name as project_name')
            ->find();
        if (!$post) $this->error('岗位不存在');

        // 获取岗位要求
        $requirements = D('PostRequirements')->getRequirements($postId, $noticeId);

        // 筛选条件
        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
        ];

        // 分页参数
        $page = intval(I('get.p', 1));
        $limit = 20;

        // 获取匹配结果
        $matchObj = D('ResumePostMatch');
        $list = $matchObj->getPostMatches($postId, $noticeId, $filters, $page, $limit);

        // 获取总数
        $where = ['post_id' => $postId, 'notice_id' => $noticeId];
        if ($filters['is_qualified'] !== '') $where['is_qualified'] = $filters['is_qualified'];
        if ($filters['min_score'] !== '') $where['match_score'] = ['egt', $filters['min_score']];
        
        $count = $matchObj->where($where)->count();
        $pageObj = $this->page($count, $limit);

        $this->assign('post', $post);
        $this->assign('requirements', $requirements);
        $this->assign('list', $list);
        $this->assign('page', $pageObj->show());
        $this->assign('filters', $filters);
        $this->assign('qualifiedList', $matchObj->is_qualified);
        $this->display();
    }

    /**
     * 查看匹配详情
     */
    public function matchDetail()
    {
        $userJobId = intval(I('get.user_job_id'));
        $postId = intval(I('get.post_id'));
        $noticeId = intval(I('get.notice_id'));
        
        if (!$userJobId || !$postId || !$noticeId) $this->error('参数错误');

        // 获取匹配记录
        $match = D('ResumePostMatch')->where([
            'user_job_id' => $userJobId,
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->find();
        if (!$match) $this->error('匹配记录不存在');

        // 获取简历信息
        $resume = D('UserJob')->find($userJobId);
        if (!$resume) $this->error('简历不存在');

        // 获取岗位信息
        $post = D('ProjectPost')->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.id' => $postId])
            ->field('pp.*, p.name as project_name')
            ->find();

        // 获取岗位要求
        $requirements = D('PostRequirements')->getRequirements($postId, $noticeId);

        // 解析匹配详情
        $matchDetails = D('ResumePostMatch')->parseMatchDetails($match['match_details']);

        $this->assign('match', $match);
        $this->assign('resume', $resume);
        $this->assign('post', $post);
        $this->assign('requirements', $requirements);
        $this->assign('matchDetails', $matchDetails);
        $this->display();
    }

    /**
     * 执行简历匹配
     */
    public function executeMatch()
    {
        $noticeId = intval(I('post.notice_id'));
        if (!$noticeId) $this->error('参数错误');

        $options = [
            'clear_old' => I('post.clear_old', 0),
            'resume_ids' => I('post.resume_ids', []),
        ];

        try {
            $service = new ResumeMatchService();
            $stats = $service->executeMatching($noticeId, $options);
            
            $this->success('匹配完成！' . 
                "处理简历{$stats['total_resumes']}份，" .
                "生成匹配记录{$stats['matched_resumes']}条，" .
                "符合要求{$stats['qualified_resumes']}条，" .
                "耗时{$stats['processing_time']}秒", 
                U('resumematch/index', ['notice_id' => $noticeId])
            );
        } catch (\Exception $e) {
            $this->error('匹配失败：' . $e->getMessage());
        }
    }

    /**
     * 匹配设置页面
     */
    public function matchSettings()
    {
        $noticeId = intval(I('get.notice_id'));
        if (!$noticeId) $this->error('参数错误');

        $notice = D('RecruitmentNotice')->find($noticeId);
        if (!$notice) $this->error('招聘公告不存在');

        // 获取现有匹配记录数量
        $existingCount = D('ResumePostMatch')->where(['notice_id' => $noticeId])->count();

        // 获取可匹配的简历数量
        $totalResumes = D('UserJob')->where(['job_state' => ['in', [0, 1]]])->count();

        $this->assign('notice', $notice);
        $this->assign('existingCount', $existingCount);
        $this->assign('totalResumes', $totalResumes);
        $this->display();
    }

    /**
     * 导出匹配结果
     */
    public function exportMatches()
    {
        $noticeId = intval(I('get.notice_id'));
        if (!$noticeId) $this->error('参数错误');

        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
            'post_id' => I('get.post_id', ''),
        ];

        // 获取所有匹配结果（不分页）
        $matchObj = D('ResumePostMatch');
        $list = $matchObj->getNoticeMatches($noticeId, $filters, 1, 10000);

        if (empty($list)) {
            $this->error('没有匹配结果可导出');
        }

        // 设置导出头
        $filename = '招聘匹配结果_' . date('YmdHis') . '.csv';
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        // 输出CSV内容
        $output = fopen('php://output', 'w');
        
        // 输出BOM头，解决中文乱码
        fwrite($output, "\xEF\xBB\xBF");
        
        // 输出表头
        fputcsv($output, [
            '姓名', '电话', '性别', '年龄', '学历', '专业', 
            '项目名称', '岗位名称', '匹配分数', '是否符合', '匹配时间'
        ]);

        // 输出数据
        $genderText = ['', '男', '女'];
        $educationText = ['', '中专', '大专', '本科', '研究生', '硕士'];
        $qualifiedText = ['不符合', '符合'];
        
        foreach ($list as $row) {
            fputcsv($output, [
                $row['name'],
                $row['phone'],
                $genderText[$row['gender']] ?? '',
                $row['age'],
                $educationText[$row['education_level']] ?? '',
                $row['major'],
                $row['project_name'],
                $row['job_name'],
                $row['match_score'],
                $qualifiedText[$row['is_qualified']],
                date('Y-m-d H:i:s', $row['create_time'])
            ]);
        }

        fclose($output);
        exit;
    }
}
