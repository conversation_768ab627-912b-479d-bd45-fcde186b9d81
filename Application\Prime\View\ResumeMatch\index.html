<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简历匹配结果</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                简历匹配结果
                <small>{$notice.title}</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentnotice/index')}">招聘公告管理</a></li>
                <li class="active">匹配结果</li>
            </ol>
        </section>

        <section class="content">
            <!-- 统计信息 -->
            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-aqua"><i class="fa fa-files-o"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">总匹配数</span>
                            <span class="info-box-number">{$stats.total}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-green"><i class="fa fa-check"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">符合要求</span>
                            <span class="info-box-number">{$stats.qualified}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-red"><i class="fa fa-times"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">不符合要求</span>
                            <span class="info-box-number">{$stats.unqualified}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-yellow"><i class="fa fa-star"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">平均分数</span>
                            <span class="info-box-number">{$stats.avg_score}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分数段统计 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">分数段分布</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="progress-group">
                                        <span class="progress-text">优秀 (90-100分)</span>
                                        <span class="float-right"><b>{$stats.score_ranges.excellent}</b>/{$stats.total}</span>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-green" style="width: {$stats.total > 0 ? ($stats.score_ranges.excellent / $stats.total * 100) : 0}%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-group">
                                        <span class="progress-text">良好 (80-89分)</span>
                                        <span class="float-right"><b>{$stats.score_ranges.good}</b>/{$stats.total}</span>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-blue" style="width: {$stats.total > 0 ? ($stats.score_ranges.good / $stats.total * 100) : 0}%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-group">
                                        <span class="progress-text">一般 (60-79分)</span>
                                        <span class="float-right"><b>{$stats.score_ranges.fair}</b>/{$stats.total}</span>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-yellow" style="width: {$stats.total > 0 ? ($stats.score_ranges.fair / $stats.total * 100) : 0}%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-group">
                                        <span class="progress-text">较差 (60分以下)</span>
                                        <span class="float-right"><b>{$stats.score_ranges.poor}</b>/{$stats.total}</span>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-red" style="width: {$stats.total > 0 ? ($stats.score_ranges.poor / $stats.total * 100) : 0}%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box">
                <div class="box-header with-border">
                    <h3 class="box-title">匹配结果列表</h3>
                    <div class="box-tools pull-right">
                        <a href="{:U('resumematch/matchSettings', array('notice_id'=>$notice['id']))}" 
                           class="btn btn-primary btn-sm">
                            <i class="fa fa-cogs"></i> 重新匹配
                        </a>
                        <a href="{:U('resumematch/exportMatches', $filters + array('notice_id'=>$notice['id']))}" 
                           class="btn btn-success btn-sm">
                            <i class="fa fa-download"></i> 导出结果
                        </a>
                    </div>
                </div>

                <!-- 筛选表单 -->
                <div class="box-body">
                    <form method="get" class="form-inline" style="margin-bottom: 15px;">
                        <input type="hidden" name="notice_id" value="{$notice.id}">
                        <div class="form-group">
                            <select name="is_qualified" class="form-control">
                                <option value="">全部结果</option>
                                <volist name="qualifiedList" id="v" key="k">
                                    <option value="{$k}" <if condition="$filters['is_qualified'] eq $k">selected</if>>{$v.text}</option>
                                </volist>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="number" name="min_score" value="{$filters.min_score}" 
                                   class="form-control" placeholder="最低分数" min="0" max="100">
                        </div>
                        <div class="form-group">
                            <select name="post_id" class="form-control">
                                <option value="">全部岗位</option>
                                <volist name="notice.posts" id="post">
                                    <option value="{$post.id}" <if condition="$filters['post_id'] eq $post['id']">selected</if>>
                                        {$post.job_name}
                                    </option>
                                </volist>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-default">筛选</button>
                        <a href="{:U('resumematch/index', array('notice_id'=>$notice['id']))}" class="btn btn-default">重置</a>
                    </form>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>简历信息</th>
                                    <th>岗位信息</th>
                                    <th>匹配分数</th>
                                    <th>是否符合</th>
                                    <th>匹配时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <volist name="list" id="vo">
                                    <tr>
                                        <td>
                                            <strong>{$vo.name}</strong><br>
                                            <small class="text-muted">
                                                {$vo.phone}<br>
                                                <if condition="$vo.gender eq 1">男<elseif condition="$vo.gender eq 2"/>女</if>
                                                <if condition="$vo.age"> / {$vo.age}岁</if>
                                            </small>
                                        </td>
                                        <td>
                                            <strong>{$vo.job_name}</strong><br>
                                            <small class="text-muted">{$vo.project_name}</small>
                                        </td>
                                        <td>
                                            <span class="badge 
                                                <if condition='$vo.match_score gte 90'>bg-green
                                                <elseif condition='$vo.match_score gte 80'/>bg-blue
                                                <elseif condition='$vo.match_score gte 60'/>bg-yellow
                                                <else/>bg-red
                                                </if>">
                                                {$vo.match_score}分
                                            </span>
                                        </td>
                                        <td>
                                            <span class="label label-{$qualifiedList[$vo.is_qualified].style}">
                                                {$qualifiedList[$vo.is_qualified].text}
                                            </span>
                                        </td>
                                        <td>{$vo.create_time|date="Y-m-d H:i",###}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{:U('resumematch/matchDetail', array('user_job_id'=>$vo['user_job_id'], 'post_id'=>$vo['post_id'], 'notice_id'=>$vo['notice_id']))}" 
                                                   class="btn btn-xs btn-info" title="查看详情">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{:U('userjob/joballinfo', array('id'=>$vo['user_job_id']))}" 
                                                   class="btn btn-xs btn-primary" title="查看简历" target="_blank">
                                                    <i class="fa fa-file-text"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </volist>
                                <if condition="empty($list)">
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">暂无匹配结果</td>
                                    </tr>
                                </if>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="box-footer clearfix">
                        {$page}
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

</body>
</html>
