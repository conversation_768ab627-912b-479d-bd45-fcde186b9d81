<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>匹配详情</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                简历匹配详情
                <small>详细匹配分析</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentnotice/index')}">招聘公告管理</a></li>
                <li><a href="{:U('resumematch/index', array('notice_id'=>$match['notice_id']))}">匹配结果</a></li>
                <li class="active">匹配详情</li>
            </ol>
        </section>

        <section class="content">
            <div class="row">
                <!-- 匹配概览 -->
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">匹配概览</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon 
                                            <if condition='$match.match_score gte 90'>bg-green
                                            <elseif condition='$match.match_score gte 80'/>bg-blue
                                            <elseif condition='$match.match_score gte 60'/>bg-yellow
                                            <else/>bg-red
                                            </if>">
                                            <i class="fa fa-star"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">匹配分数</span>
                                            <span class="info-box-number">{$match.match_score}分</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon 
                                            <if condition='$match.is_qualified eq 1'>bg-green<else/>bg-red</if>">
                                            <i class="fa <if condition='$match.is_qualified eq 1'>fa-check<else/>fa-times</if>"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">是否符合</span>
                                            <span class="info-box-number">
                                                <if condition='$match.is_qualified eq 1'>符合<else/>不符合</if>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h4>基本信息</h4>
                                    <p><strong>简历：</strong>{$resume.name} ({$resume.phone})</p>
                                    <p><strong>岗位：</strong>{$post.job_name} ({$post.project_name})</p>
                                    <p><strong>匹配时间：</strong>{$match.create_time|date="Y-m-d H:i:s",###}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 简历信息 -->
                <div class="col-md-6">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">简历信息</h3>
                        </div>
                        <div class="box-body">
                            <table class="table table-bordered">
                                <tr>
                                    <td width="30%"><strong>姓名</strong></td>
                                    <td>{$resume.name}</td>
                                </tr>
                                <tr>
                                    <td><strong>性别</strong></td>
                                    <td>
                                        <if condition="$resume.gender eq 1">男
                                        <elseif condition="$resume.gender eq 2"/>女
                                        <else/>未知
                                        </if>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>年龄</strong></td>
                                    <td>{$resume.age|default="未知"}岁</td>
                                </tr>
                                <tr>
                                    <td><strong>身高</strong></td>
                                    <td>{$resume.height|default="未知"}cm</td>
                                </tr>
                                <tr>
                                    <td><strong>学历</strong></td>
                                    <td>
                                        <switch name="resume.education_level">
                                            <case value="1">中专</case>
                                            <case value="2">大专</case>
                                            <case value="3">本科</case>
                                            <case value="4">研究生</case>
                                            <case value="5">硕士</case>
                                            <default/>未知
                                        </switch>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>专业</strong></td>
                                    <td>{$resume.major|default="未填写"}</td>
                                </tr>
                                <tr>
                                    <td><strong>工作经验</strong></td>
                                    <td>{$resume.work_experience_years|default="0"}年</td>
                                </tr>
                                <tr>
                                    <td><strong>联系电话</strong></td>
                                    <td>{$resume.phone}</td>
                                </tr>
                            </table>
                            <div class="text-center">
                                <a href="{:U('userjob/joballinfo', array('id'=>$resume['id']))}" 
                                   class="btn btn-primary" target="_blank">
                                    <i class="fa fa-file-text"></i> 查看完整简历
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 岗位要求 -->
                <div class="col-md-6">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">岗位要求</h3>
                        </div>
                        <div class="box-body">
                            <if condition="$requirements">
                                <table class="table table-bordered">
                                    <if condition="$requirements.min_age || $requirements.max_age">
                                        <tr>
                                            <td width="30%"><strong>年龄要求</strong></td>
                                            <td>
                                                <if condition="$requirements.min_age && $requirements.max_age">
                                                    {$requirements.min_age}-{$requirements.max_age}岁
                                                <elseif condition="$requirements.min_age"/>
                                                    {$requirements.min_age}岁以上
                                                <elseif condition="$requirements.max_age"/>
                                                    {$requirements.max_age}岁以下
                                                </if>
                                            </td>
                                        </tr>
                                    </if>
                                    <if condition="$requirements.gender gt 0">
                                        <tr>
                                            <td><strong>性别要求</strong></td>
                                            <td>
                                                <if condition="$requirements.gender eq 1">男
                                                <elseif condition="$requirements.gender eq 2"/>女
                                                </if>
                                            </td>
                                        </tr>
                                    </if>
                                    <if condition="$requirements.min_height || $requirements.max_height">
                                        <tr>
                                            <td><strong>身高要求</strong></td>
                                            <td>
                                                <if condition="$requirements.min_height && $requirements.max_height">
                                                    {$requirements.min_height}-{$requirements.max_height}cm
                                                <elseif condition="$requirements.min_height"/>
                                                    {$requirements.min_height}cm以上
                                                <elseif condition="$requirements.max_height"/>
                                                    {$requirements.max_height}cm以下
                                                </if>
                                            </td>
                                        </tr>
                                    </if>
                                    <if condition="$requirements.education_level gt 0">
                                        <tr>
                                            <td><strong>学历要求</strong></td>
                                            <td>
                                                <switch name="requirements.education_level">
                                                    <case value="1">中专及以上</case>
                                                    <case value="2">大专及以上</case>
                                                    <case value="3">本科及以上</case>
                                                    <case value="4">研究生及以上</case>
                                                    <case value="5">硕士</case>
                                                </switch>
                                            </td>
                                        </tr>
                                    </if>
                                    <if condition="$requirements.major_keywords">
                                        <tr>
                                            <td><strong>专业要求</strong></td>
                                            <td>{$requirements.major_keywords}</td>
                                        </tr>
                                    </if>
                                    <if condition="$requirements.min_work_years || $requirements.max_work_years">
                                        <tr>
                                            <td><strong>工作经验</strong></td>
                                            <td>
                                                <if condition="$requirements.min_work_years && $requirements.max_work_years">
                                                    {$requirements.min_work_years}-{$requirements.max_work_years}年
                                                <elseif condition="$requirements.min_work_years"/>
                                                    {$requirements.min_work_years}年以上
                                                <elseif condition="$requirements.max_work_years"/>
                                                    {$requirements.max_work_years}年以下
                                                </if>
                                            </td>
                                        </tr>
                                    </if>
                                </table>
                            <else/>
                                <p class="text-muted">该岗位尚未配置筛选要求</p>
                            </if>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细匹配分析 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">详细匹配分析</h3>
                        </div>
                        <div class="box-body">
                            <if condition="$matchDetails">
                                <div class="row">
                                    <volist name="matchDetails" id="detail" key="category">
                                        <div class="col-md-6" style="margin-bottom: 20px;">
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <switch name="category">
                                                            <case value="age">年龄匹配</case>
                                                            <case value="gender">性别匹配</case>
                                                            <case value="height">身高匹配</case>
                                                            <case value="education">学历匹配</case>
                                                            <case value="major">专业匹配</case>
                                                            <case value="work_experience">工作经验匹配</case>
                                                            <case value="other">其他条件匹配</case>
                                                            <default/>{$category}
                                                        </switch>
                                                        <span class="pull-right">
                                                            <span class="badge 
                                                                <if condition='$detail.qualified'>bg-green<else/>bg-red</if>">
                                                                {$detail.score}分
                                                            </span>
                                                        </span>
                                                    </h4>
                                                </div>
                                                <div class="panel-body">
                                                    <p class="<if condition='$detail.qualified'>text-success<else/>text-danger</if>">
                                                        <i class="fa <if condition='$detail.qualified'>fa-check<else/>fa-times</if>"></i>
                                                        {$detail.message}
                                                    </p>
                                                    <div class="progress progress-sm">
                                                        <div class="progress-bar 
                                                            <if condition='$detail.qualified'>progress-bar-success<else/>progress-bar-danger</if>" 
                                                             style="width: {$detail.score * 4}%">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </volist>
                                </div>
                            <else/>
                                <p class="text-muted">暂无详细匹配分析数据</p>
                            </if>
                        </div>
                        <div class="box-footer">
                            <a href="{:U('resumematch/index', array('notice_id'=>$match['notice_id']))}" 
                               class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> 返回匹配结果
                            </a>
                            <a href="{:U('userjob/joballinfo', array('id'=>$resume['id']))}" 
                               class="btn btn-primary" target="_blank">
                                <i class="fa fa-file-text"></i> 查看完整简历
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

</body>
</html>
