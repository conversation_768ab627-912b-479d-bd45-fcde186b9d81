<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>选择招聘公告</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                选择招聘公告
                <small>查看匹配结果</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentsystem/index')}">招聘筛选系统</a></li>
                <li class="active">选择公告</li>
            </ol>
        </section>

        <section class="content">
            <div class="row">
                <div class="col-md-10 col-md-offset-1">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">请选择要查看匹配结果的招聘公告</h3>
                        </div>
                        <div class="box-body">
                            <if condition="empty($notices)">
                                <div class="text-center">
                                    <i class="fa fa-exclamation-triangle fa-3x text-muted"></i>
                                    <h4 class="text-muted">暂无启用的招聘公告</h4>
                                    <p class="text-muted">请先创建并启用招聘公告</p>
                                    <a href="{:U('recruitmentsystem/edit')}" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> 创建招聘公告
                                    </a>
                                </div>
                            <else/>
                                <div class="row">
                                    <volist name="notices" id="notice">
                                        <div class="col-md-6" style="margin-bottom: 20px;">
                                            <div class="panel panel-default">
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <h4>
                                                                {$notice.title}
                                                                <span class="label label-success">启用中</span>
                                                            </h4>
                                                            <p class="text-muted">
                                                                <if condition="$notice.company_name">
                                                                    <i class="fa fa-building"></i> {$notice.company_name}<br>
                                                                </if>
                                                                <i class="fa fa-calendar"></i> 创建时间：{$notice.create_time|date="Y-m-d",###}<br>
                                                                <if condition="$notice.description">
                                                                    <i class="fa fa-info-circle"></i> {$notice.description|mb_substr=0,50,'utf-8'}...
                                                                </if>
                                                            </p>
                                                        </div>
                                                        <div class="col-md-4 text-right">
                                                            <div class="btn-group-vertical">
                                                                <a href="{:U('recruitmentsystem/matches', array('notice_id'=>$notice['id']))}" 
                                                                   class="btn btn-primary btn-sm">
                                                                    <i class="fa fa-search"></i> 查看匹配结果
                                                                </a>
                                                                <a href="{:U('recruitmentsystem/matchSettings', array('notice_id'=>$notice['id']))}" 
                                                                   class="btn btn-success btn-sm">
                                                                    <i class="fa fa-cogs"></i> 执行匹配
                                                                </a>
                                                                <a href="{:U('recruitmentsystem/requirements', array('notice_id'=>$notice['id']))}" 
                                                                   class="btn btn-warning btn-sm">
                                                                    <i class="fa fa-cogs"></i> 配置要求
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </volist>
                                </div>
                            </if>
                        </div>
                        <div class="box-footer">
                            <a href="{:U('recruitmentsystem/index')}" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> 返回系统首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

</body>
</html>
