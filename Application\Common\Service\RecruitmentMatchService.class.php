<?php
namespace Common\Service;

/**
 * 招聘匹配服务类
 * 核心匹配算法实现
 */
class RecruitmentMatchService
{
    /**
     * 执行简历筛选匹配
     */
    public function executeMatching($noticeId, $options = [])
    {
        $stats = [
            'total_resumes' => 0,
            'matched_resumes' => 0,
            'qualified_resumes' => 0,
            'processing_time' => 0
        ];

        $startTime = microtime(true);

        try {
            // 获取招聘公告信息
            $systemModel = new \Common\Model\RecruitmentSystemModel();
            $notice = M('recruitment_notice')->find($noticeId);
            if (!$notice || $notice['status'] != 1) {
                throw new \Exception('招聘公告不存在或已停用');
            }

            // 获取公告关联的岗位和要求
            $posts = M('recruitment_notice_post')
                ->alias('rnp')
                ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
                ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
                ->where(['rnp.notice_id' => $noticeId])
                ->field('pp.*, p.name as project_name')
                ->select();

            if (empty($posts)) {
                throw new \Exception('该公告尚未关联任何岗位');
            }

            // 获取每个岗位的筛选要求
            $postRequirements = [];
            foreach ($posts as $post) {
                $requirements = M('post_requirements')->where([
                    'post_id' => $post['id'], 
                    'notice_id' => $noticeId
                ])->find();
                if ($requirements) {
                    $postRequirements[$post['id']] = [
                        'post' => $post,
                        'requirements' => $requirements
                    ];
                }
            }

            if (empty($postRequirements)) {
                throw new \Exception('该公告的岗位尚未配置筛选要求');
            }

            // 获取所有简历
            $resumeWhere = ['job_state' => ['in', [0, 1]]];
            if (isset($options['resume_ids']) && !empty($options['resume_ids'])) {
                $resumeWhere['id'] = ['in', $options['resume_ids']];
            }

            $resumes = M('user_job')->where($resumeWhere)->select();
            $stats['total_resumes'] = count($resumes);

            // 清除旧的匹配记录
            if (isset($options['clear_old']) && $options['clear_old']) {
                M('resume_post_match')->where(['notice_id' => $noticeId])->delete();
            }

            // 执行匹配
            foreach ($resumes as $resume) {
                foreach ($postRequirements as $postId => $postData) {
                    $matchResult = $this->matchResumeToPost($resume, $postData['post'], $postData['requirements']);
                    
                    // 保存匹配结果
                    $matchData = [
                        'user_job_id' => $resume['id'],
                        'post_id' => $postId,
                        'notice_id' => $noticeId,
                        'match_score' => $matchResult['score'],
                        'match_details' => json_encode($matchResult['details'], JSON_UNESCAPED_UNICODE),
                        'is_qualified' => $matchResult['qualified'] ? 1 : 0,
                        'create_time' => time(),
                    ];

                    $systemModel->saveMatch($matchData);
                    
                    $stats['matched_resumes']++;
                    if ($matchResult['qualified']) {
                        $stats['qualified_resumes']++;
                    }
                }
            }

            $stats['processing_time'] = round(microtime(true) - $startTime, 2);
            return $stats;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 匹配单个简历到岗位
     */
    private function matchResumeToPost($resume, $post, $requirements)
    {
        $score = 0;
        $maxScore = 0;
        $details = [];
        $qualified = true;

        // 1. 年龄匹配 (权重: 15分)
        $ageResult = $this->matchAge($resume, $requirements);
        $score += $ageResult['score'];
        $maxScore += 15;
        $details['age'] = $ageResult;
        if (!$ageResult['qualified']) $qualified = false;

        // 2. 性别匹配 (权重: 10分)
        $genderResult = $this->matchGender($resume, $requirements);
        $score += $genderResult['score'];
        $maxScore += 10;
        $details['gender'] = $genderResult;
        if (!$genderResult['qualified']) $qualified = false;

        // 3. 身高匹配 (权重: 10分)
        $heightResult = $this->matchHeight($resume, $requirements);
        $score += $heightResult['score'];
        $maxScore += 10;
        $details['height'] = $heightResult;
        if (!$heightResult['qualified']) $qualified = false;

        // 4. 学历匹配 (权重: 25分)
        $educationResult = $this->matchEducation($resume, $requirements);
        $score += $educationResult['score'];
        $maxScore += 25;
        $details['education'] = $educationResult;
        if (!$educationResult['qualified']) $qualified = false;

        // 5. 专业匹配 (权重: 20分)
        $majorResult = $this->matchMajor($resume, $requirements);
        $score += $majorResult['score'];
        $maxScore += 20;
        $details['major'] = $majorResult;

        // 6. 工作经验匹配 (权重: 15分)
        $workResult = $this->matchWorkExperience($resume, $requirements);
        $score += $workResult['score'];
        $maxScore += 15;
        $details['work_experience'] = $workResult;

        // 7. 其他条件匹配 (权重: 5分)
        $otherResult = $this->matchOtherRequirements($resume, $requirements);
        $score += $otherResult['score'];
        $maxScore += 5;
        $details['other'] = $otherResult;

        // 计算最终分数 (0-100)
        $finalScore = $maxScore > 0 ? round(($score / $maxScore) * 100, 2) : 0;

        return [
            'score' => $finalScore,
            'qualified' => $qualified,
            'details' => $details
        ];
    }

    /**
     * 年龄匹配
     */
    private function matchAge($resume, $requirements)
    {
        $result = ['score' => 15, 'qualified' => true, 'message' => '年龄符合要求'];

        $age = $this->calculateAge($resume['birthdate'] ?? $resume['id_number'] ?? '');
        if (!$age) {
            return ['score' => 0, 'qualified' => false, 'message' => '无法获取年龄信息'];
        }

        $minAge = $requirements['min_age'];
        $maxAge = $requirements['max_age'];

        if ($minAge && $age < $minAge) {
            return ['score' => 0, 'qualified' => false, 'message' => "年龄{$age}岁，低于最低要求{$minAge}岁"];
        }

        if ($maxAge && $age > $maxAge) {
            return ['score' => 0, 'qualified' => false, 'message' => "年龄{$age}岁，超过最高要求{$maxAge}岁"];
        }

        $result['message'] = "年龄{$age}岁，符合要求";
        return $result;
    }

    /**
     * 性别匹配
     */
    private function matchGender($resume, $requirements)
    {
        $result = ['score' => 10, 'qualified' => true, 'message' => '性别符合要求'];

        if (!$requirements['gender'] || $requirements['gender'] == 0) {
            $result['message'] = '性别不限';
            return $result;
        }

        $resumeGender = $resume['gender'] ?? 0;
        if ($resumeGender != $requirements['gender']) {
            $genderText = ['', '男', '女'];
            return [
                'score' => 0, 
                'qualified' => false, 
                'message' => "性别不符，要求{$genderText[$requirements['gender']]}，实际{$genderText[$resumeGender]}"
            ];
        }

        return $result;
    }

    /**
     * 身高匹配
     */
    private function matchHeight($resume, $requirements)
    {
        $result = ['score' => 10, 'qualified' => true, 'message' => '身高符合要求'];

        $height = intval($resume['height'] ?? 0);
        if (!$height) {
            return ['score' => 5, 'qualified' => true, 'message' => '身高信息缺失'];
        }

        $minHeight = $requirements['min_height'];
        $maxHeight = $requirements['max_height'];

        if ($minHeight && $height < $minHeight) {
            return ['score' => 0, 'qualified' => false, 'message' => "身高{$height}cm，低于最低要求{$minHeight}cm"];
        }

        if ($maxHeight && $height > $maxHeight) {
            return ['score' => 0, 'qualified' => false, 'message' => "身高{$height}cm，超过最高要求{$maxHeight}cm"];
        }

        $result['message'] = "身高{$height}cm，符合要求";
        return $result;
    }

    /**
     * 学历匹配
     */
    private function matchEducation($resume, $requirements)
    {
        $result = ['score' => 25, 'qualified' => true, 'message' => '学历符合要求'];

        if (!$requirements['education_level'] || $requirements['education_level'] == 0) {
            $result['message'] = '学历不限';
            return $result;
        }

        $resumeEducation = intval($resume['education_level'] ?? 0);
        $requiredEducation = intval($requirements['education_level']);

        if ($resumeEducation < $requiredEducation) {
            $educationText = ['不限', '中专及以上', '大专及以上', '本科及以上', '研究生及以上', '硕士'];
            return [
                'score' => 0, 
                'qualified' => false, 
                'message' => "学历不符，要求{$educationText[$requiredEducation]}，实际{$educationText[$resumeEducation]}"
            ];
        }

        // 超出要求可以加分
        if ($resumeEducation > $requiredEducation) {
            $result['score'] = min(30, 25 + ($resumeEducation - $requiredEducation) * 2);
        }

        return $result;
    }

    /**
     * 专业匹配
     */
    private function matchMajor($resume, $requirements)
    {
        $result = ['score' => 10, 'qualified' => true, 'message' => '专业匹配度一般'];

        $majorKeywords = $requirements['major_keywords'];
        if (empty($majorKeywords)) {
            $result['message'] = '专业不限';
            $result['score'] = 20;
            return $result;
        }

        $resumeMajor = $resume['major'] ?? '';
        if (empty($resumeMajor)) {
            $result['message'] = '专业信息缺失';
            return $result;
        }

        // 关键词匹配
        $keywords = explode(',', $majorKeywords);
        $matchCount = 0;
        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if ($keyword && strpos($resumeMajor, $keyword) !== false) {
                $matchCount++;
            }
        }

        if ($matchCount > 0) {
            $result['score'] = min(20, 10 + $matchCount * 5);
            $result['message'] = "专业匹配，匹配{$matchCount}个关键词";
        } else {
            $result['message'] = "专业不匹配，简历专业：{$resumeMajor}";
        }

        return $result;
    }

    /**
     * 工作经验匹配
     */
    private function matchWorkExperience($resume, $requirements)
    {
        $result = ['score' => 15, 'qualified' => true, 'message' => '工作经验符合要求'];

        $workYears = intval($resume['work_experience_years'] ?? 0);
        $minYears = $requirements['min_work_years'];
        $maxYears = $requirements['max_work_years'];

        if ($minYears && $workYears < $minYears) {
            $result['score'] = max(0, 15 - ($minYears - $workYears) * 3);
            $result['message'] = "工作经验{$workYears}年，低于要求{$minYears}年";
        } elseif ($maxYears && $workYears > $maxYears) {
            $result['score'] = max(10, 15 - ($workYears - $maxYears) * 2);
            $result['message'] = "工作经验{$workYears}年，超过要求{$maxYears}年";
        } else {
            $result['message'] = "工作经验{$workYears}年，符合要求";
        }

        return $result;
    }

    /**
     * 其他条件匹配
     */
    private function matchOtherRequirements($resume, $requirements)
    {
        $score = 5;
        $messages = [];

        // 健康状况
        if ($requirements['health_status']) {
            $resumeHealth = $resume['health_status'] ?? '';
            if ($resumeHealth && strpos($resumeHealth, $requirements['health_status']) !== false) {
                $messages[] = '健康状况符合';
            } else {
                $score -= 1;
                $messages[] = '健康状况待确认';
            }
        }

        // 恐高要求
        if ($requirements['is_afraid_heights'] == 1) {
            $resumeAfraidHeights = $resume['is_afraid_heights'] ?? 0;
            if ($resumeAfraidHeights == 1) {
                $score -= 2;
                $messages[] = '恐高不符合要求';
            } else {
                $messages[] = '恐高要求符合';
            }
        }

        return [
            'score' => max(0, $score),
            'qualified' => true,
            'message' => implode('；', $messages) ?: '其他条件符合'
        ];
    }

    /**
     * 计算年龄
     */
    private function calculateAge($birthdate)
    {
        if (empty($birthdate)) return 0;

        // 如果是身份证号，提取出生日期
        if (strlen($birthdate) == 18) {
            $birthdate = substr($birthdate, 6, 8);
            $birthdate = substr($birthdate, 0, 4) . '-' . substr($birthdate, 4, 2) . '-' . substr($birthdate, 6, 2);
        }

        $birth = strtotime($birthdate);
        if (!$birth) return 0;

        return floor((time() - $birth) / (365 * 24 * 3600));
    }
}
