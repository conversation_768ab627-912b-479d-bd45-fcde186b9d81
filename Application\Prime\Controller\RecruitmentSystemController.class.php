<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;
use Common\Service\RecruitmentMatchService;

/**
 * 招聘筛选系统统一控制器
 * 整合所有招聘相关功能
 */
class RecruitmentSystemController extends PrimeController
{
    private $systemModel;

    public function _initialize()
    {
        parent::_initialize();
        $this->systemModel = new \Common\Model\RecruitmentSystemModel();
    }

    // ==================== 招聘公告管理 ====================

    /**
     * 招聘公告列表
     */
    public function index()
    {
        $c_kw = [
            'title' => '公告标题',
            'company_name' => '招聘单位',
            'id' => 'ID',
        ];

        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");

        if ($s_status !== '') $where['status'] = $s_status;

        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['title', 'company_name'])) {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }

        $count = M('recruitment_notice')->where($where)->count();
        $page = $this->page($count, 20);

        $list = $this->systemModel->getNoticeList($where, $page->firstRow, $page->listRows);

        // 获取统计信息
        $stats = [
            'total' => M('recruitment_notice')->count(),
            'active' => M('recruitment_notice')->where(['status' => 1])->count(),
            'inactive' => M('recruitment_notice')->where(['status' => 0])->count(),
        ];

        $this->assign('list', $list);
        $this->assign("page", $page->show());
        $this->assign('stats', $stats);
        $this->assign('statusList', $this->systemModel->status);
        $this->assign('c_kw', $c_kw);
        $this->assign('s_kw', $s_kw);
        $this->assign('s_val', $s_val);
        $this->assign('s_status', $s_status);
        $this->display();
    }

    /**
     * 添加/编辑招聘公告
     */
    public function edit()
    {
        $id = intval(I('get.id'));
        
        if ($id) {
            $row = $this->systemModel->getNoticeDetail($id);
            if (!$row) $this->error('参数错误');
            $this->assign('row', $row);
            
            $relatedPostIds = array_column($row['posts'], 'id');
            $this->assign('relatedPostIds', $relatedPostIds);
        }

        if (IS_POST) {
            $data = I('post.');
            
            if (empty($data['title'])) {
                $this->error('请填写公告标题');
            }

            $postIds = I('post.post_ids', []);
            $result = $this->systemModel->saveNotice($data, $postIds);

            if ($result) {
                $this->success("操作成功", U("recruitmentsystem/index"));
            } else {
                $this->error('操作失败');
            }
        }

        $availablePosts = $this->systemModel->getAvailablePosts();
        $this->assign('availablePosts', $availablePosts);
        $this->assign('statusList', $this->systemModel->status);
        $this->display();
    }

    /**
     * 删除招聘公告
     */
    public function del()
    {
        $id = I('get.id/d', 0);
        if ($id <= 0) $this->error('参数错误');

        $row = M('recruitment_notice')->find($id);
        if (!$row) $this->error('记录不存在');

        $matchCount = M('resume_post_match')->where(['notice_id' => $id])->count();
        if ($matchCount > 0) {
            $this->error('该公告已有匹配记录，无法删除');
        }

        $result = $this->systemModel->deleteNotice($id);
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 状态变更
     */
    public function cgstat()
    {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        
        if (!array_key_exists($status, $this->systemModel->status)) $this->error('参数错误');

        $result = M('recruitment_notice')->where("id=".$id)->save(['status' => $status, 'update_time' => time()]);
        if ($result !== false) {
            $this->success('操作成功');
        } else {
            $this->error('操作失败');
        }
    }

    // ==================== 岗位要求配置 ====================

    /**
     * 配置岗位要求
     */
    public function requirements()
    {
        $noticeId = intval(I('get.notice_id'));
        $postId = intval(I('get.post_id'));
        
        if (!$noticeId) $this->error('参数错误');

        $notice = M('recruitment_notice')->find($noticeId);
        if (!$notice) $this->error('公告不存在');

        $posts = M('recruitment_notice_post')
            ->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $noticeId])
            ->field('pp.*, p.name as project_name, rnp.id as relation_id')
            ->select();

        if (empty($posts)) $this->error('该公告尚未关联任何岗位');

        $currentPost = null;
        $requirements = null;
        if ($postId) {
            foreach ($posts as $post) {
                if ($post['id'] == $postId) {
                    $currentPost = $post;
                    break;
                }
            }
            if (!$currentPost) $this->error('岗位不存在或未关联到该公告');
            
            $requirements = $this->systemModel->getRequirements($postId, $noticeId);
        }

        // 为每个岗位添加要求信息
        foreach ($posts as &$post) {
            $post['requirements'] = $this->systemModel->getRequirements($post['id'], $noticeId);
        }

        if (IS_POST) {
            $data = I('post.');
            $data['notice_id'] = $noticeId;
            
            if (isset($data['batch_mode']) && $data['batch_mode'] == 1) {
                $selectedPostIds = I('post.selected_post_ids', []);
                if (empty($selectedPostIds)) {
                    $this->error('请选择要设置的岗位');
                }
                
                $result = $this->systemModel->batchSetRequirements($selectedPostIds, $noticeId, $data);
            } else {
                if (!$postId) $this->error('参数错误');
                $data['post_id'] = $postId;
                $result = $this->systemModel->saveRequirements($data);
            }

            if ($result) {
                $this->success("设置成功", U("recruitmentsystem/requirements", ['notice_id' => $noticeId]));
            } else {
                $this->error('设置失败');
            }
        }

        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('currentPost', $currentPost);
        $this->assign('requirements', $requirements);
        $this->assign('postId', $postId);
        $this->assign('genderList', $this->systemModel->gender);
        $this->assign('educationList', $this->systemModel->education_level);
        $this->display();
    }

    // ==================== 简历匹配功能 ====================

    /**
     * 匹配结果列表
     */
    public function matches()
    {
        $noticeId = intval(I('get.notice_id'));
        if (!$noticeId) {
            $notices = M('recruitment_notice')->where(['status' => 1])->order('id DESC')->select();
            $this->assign('notices', $notices);
            $this->display('select_notice');
            return;
        }

        $notice = $this->systemModel->getNoticeDetail($noticeId);
        if (!$notice) $this->error('招聘公告不存在');

        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
            'post_id' => I('get.post_id', ''),
        ];

        $where = ['notice_id' => $noticeId];
        if ($filters['is_qualified'] !== '') $where['is_qualified'] = $filters['is_qualified'];
        if ($filters['min_score'] !== '') $where['match_score'] = ['egt', $filters['min_score']];
        if ($filters['post_id']) $where['post_id'] = $filters['post_id'];

        $count = M('resume_post_match')->where($where)->count();
        $pageObj = $this->page($count, 20);

        $list = $this->systemModel->getMatchResults($noticeId, $filters, $pageObj->firstRow, $pageObj->listRows);

        $stats = $this->systemModel->getMatchStats($noticeId);

        $this->assign('notice', $notice);
        $this->assign('list', $list);
        $this->assign('page', $pageObj->show());
        $this->assign('stats', $stats);
        $this->assign('filters', $filters);
        $this->assign('qualifiedList', $this->systemModel->is_qualified);
        $this->display();
    }

    /**
     * 匹配设置页面
     */
    public function matchSettings()
    {
        $noticeId = intval(I('get.notice_id'));
        if (!$noticeId) $this->error('参数错误');

        $notice = M('recruitment_notice')->find($noticeId);
        if (!$notice) $this->error('招聘公告不存在');

        $existingCount = M('resume_post_match')->where(['notice_id' => $noticeId])->count();
        $totalResumes = M('user_job')->where(['job_state' => ['in', [0, 1]]])->count();

        $this->assign('notice', $notice);
        $this->assign('existingCount', $existingCount);
        $this->assign('totalResumes', $totalResumes);
        $this->display();
    }

    /**
     * 执行简历匹配
     */
    public function executeMatch()
    {
        $noticeId = intval(I('post.notice_id'));
        if (!$noticeId) $this->error('参数错误');

        $options = [
            'clear_old' => I('post.clear_old', 0),
            'resume_ids' => I('post.resume_ids', []),
        ];

        try {
            $service = new RecruitmentMatchService();
            $stats = $service->executeMatching($noticeId, $options);
            
            $this->success('匹配完成！' . 
                "处理简历{$stats['total_resumes']}份，" .
                "生成匹配记录{$stats['matched_resumes']}条，" .
                "符合要求{$stats['qualified_resumes']}条，" .
                "耗时{$stats['processing_time']}秒", 
                U('recruitmentsystem/matches', ['notice_id' => $noticeId])
            );
        } catch (\Exception $e) {
            $this->error('匹配失败：' . $e->getMessage());
        }
    }

    /**
     * 导出匹配结果
     */
    public function exportMatches()
    {
        $noticeId = intval(I('get.notice_id'));
        if (!$noticeId) $this->error('参数错误');

        $filters = [
            'is_qualified' => I('get.is_qualified', ''),
            'min_score' => I('get.min_score', ''),
            'post_id' => I('get.post_id', ''),
        ];

        $list = $this->systemModel->getMatchResults($noticeId, $filters, 0, 10000);

        if (empty($list)) {
            $this->error('没有匹配结果可导出');
        }

        $filename = '招聘匹配结果_' . date('YmdHis') . '.csv';
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        $output = fopen('php://output', 'w');
        fwrite($output, "\xEF\xBB\xBF");
        
        fputcsv($output, [
            '姓名', '电话', '性别', '年龄', '学历', '专业', 
            '项目名称', '岗位名称', '匹配分数', '是否符合', '匹配时间'
        ]);

        $genderText = ['', '男', '女'];
        $educationText = ['', '中专', '大专', '本科', '研究生', '硕士'];
        $qualifiedText = ['不符合', '符合'];
        
        foreach ($list as $row) {
            fputcsv($output, [
                $row['name'],
                $row['phone'],
                $genderText[$row['gender']] ?? '',
                $row['age'],
                $educationText[$row['education_level']] ?? '',
                $row['major'],
                $row['project_name'],
                $row['job_name'],
                $row['match_score'],
                $qualifiedText[$row['is_qualified']],
                date('Y-m-d H:i:s', $row['create_time'])
            ]);
        }

        fclose($output);
        exit;
    }
}
