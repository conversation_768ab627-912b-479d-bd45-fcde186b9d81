<?php
namespace Common\Model;

use Think\Model;

/**
 * 简历岗位匹配模型
 */
class ResumePostMatchModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 是否符合要求定义
     */
    public $is_qualified = [
        '0' => ['text' => '不符合', 'style' => 'danger'],
        '1' => ['text' => '符合', 'style' => 'success'],
    ];

    /**
     * 保存匹配结果
     * @param array $data 匹配数据
     * @return bool|int
     */
    public function saveMatch($data)
    {
        // 检查是否已存在匹配记录
        $existing = $this->where([
            'user_job_id' => $data['user_job_id'],
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();

        if ($existing) {
            // 更新现有记录
            $data['id'] = $existing['id'];
            return $this->save($data);
        } else {
            // 新增记录
            return $this->add($data);
        }
    }

    /**
     * 获取招聘公告的匹配结果
     * @param int $noticeId 公告ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getNoticeMatches($noticeId, $filters = [], $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        $where = ['rpm.notice_id' => $noticeId];

        // 添加筛选条件
        if (isset($filters['is_qualified']) && $filters['is_qualified'] !== '') {
            $where['rpm.is_qualified'] = $filters['is_qualified'];
        }
        if (isset($filters['min_score']) && $filters['min_score'] !== '') {
            $where['rpm.match_score'] = ['egt', $filters['min_score']];
        }
        if (isset($filters['post_id']) && $filters['post_id']) {
            $where['rpm.post_id'] = $filters['post_id'];
        }

        $list = $this->alias('rpm')
            ->join('LEFT JOIN __USER_JOB__ uj ON rpm.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($where)
            ->field('rpm.*, uj.name, uj.phone, uj.gender, uj.age, uj.education_level, uj.major, pp.job_name, p.name as project_name')
            ->order('rpm.match_score DESC, rpm.id DESC')
            ->limit($offset, $limit)
            ->select();

        return $list ?: [];
    }

    /**
     * 获取岗位的匹配结果
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getPostMatches($postId, $noticeId, $filters = [], $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        $where = [
            'rpm.post_id' => $postId,
            'rpm.notice_id' => $noticeId
        ];

        // 添加筛选条件
        if (isset($filters['is_qualified']) && $filters['is_qualified'] !== '') {
            $where['rpm.is_qualified'] = $filters['is_qualified'];
        }
        if (isset($filters['min_score']) && $filters['min_score'] !== '') {
            $where['rpm.match_score'] = ['egt', $filters['min_score']];
        }

        $list = $this->alias('rpm')
            ->join('LEFT JOIN __USER_JOB__ uj ON rpm.user_job_id = uj.id')
            ->where($where)
            ->field('rpm.*, uj.name, uj.phone, uj.gender, uj.age, uj.education_level, uj.major, uj.height, uj.work_experience_years')
            ->order('rpm.match_score DESC, rpm.id DESC')
            ->limit($offset, $limit)
            ->select();

        return $list ?: [];
    }

    /**
     * 获取简历的匹配岗位
     * @param int $userJobId 简历ID
     * @return array
     */
    public function getResumeMatches($userJobId)
    {
        return $this->alias('rpm')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON rpm.notice_id = rn.id')
            ->where(['rpm.user_job_id' => $userJobId])
            ->field('rpm.*, pp.job_name, p.name as project_name, rn.title as notice_title')
            ->order('rpm.match_score DESC')
            ->select();
    }

    /**
     * 获取匹配统计信息
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getMatchStats($noticeId)
    {
        $stats = [];

        // 总匹配数
        $stats['total'] = $this->where(['notice_id' => $noticeId])->count();

        // 符合要求的数量
        $stats['qualified'] = $this->where(['notice_id' => $noticeId, 'is_qualified' => 1])->count();

        // 不符合要求的数量
        $stats['unqualified'] = $stats['total'] - $stats['qualified'];

        // 平均匹配分数
        $avgScore = $this->where(['notice_id' => $noticeId])->avg('match_score');
        $stats['avg_score'] = round($avgScore, 2);

        // 按分数段统计
        $stats['score_ranges'] = [
            'excellent' => $this->where(['notice_id' => $noticeId, 'match_score' => ['egt', 90]])->count(), // 90分以上
            'good' => $this->where(['notice_id' => $noticeId, 'match_score' => ['between', [80, 89]]])->count(), // 80-89分
            'fair' => $this->where(['notice_id' => $noticeId, 'match_score' => ['between', [60, 79]]])->count(), // 60-79分
            'poor' => $this->where(['notice_id' => $noticeId, 'match_score' => ['lt', 60]])->count(), // 60分以下
        ];

        return $stats;
    }

    /**
     * 删除匹配记录
     * @param int $userJobId 简历ID
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @return bool
     */
    public function deleteMatch($userJobId, $postId, $noticeId)
    {
        return $this->where([
            'user_job_id' => $userJobId,
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->delete();
    }

    /**
     * 批量删除公告的所有匹配记录
     * @param int $noticeId 公告ID
     * @return bool
     */
    public function deleteNoticeMatches($noticeId)
    {
        return $this->where(['notice_id' => $noticeId])->delete();
    }

    /**
     * 获取匹配详情的可读文本
     * @param string $matchDetailsJson 匹配详情JSON
     * @return array
     */
    public function parseMatchDetails($matchDetailsJson)
    {
        if (empty($matchDetailsJson)) {
            return [];
        }

        $details = json_decode($matchDetailsJson, true);
        if (!$details) {
            return [];
        }

        return $details;
    }
}
