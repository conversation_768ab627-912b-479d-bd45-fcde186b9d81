<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>招聘公告管理</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>招聘公告管理 <small>智能筛选系统</small></h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li class="active">招聘公告管理</li>
            </ol>
        </section>

        <section class="content">
            <!-- 统计信息 -->
            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-aqua"><i class="fa fa-file-text"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">总公告数</span>
                            <span class="info-box-number">{$stats.total}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-green"><i class="fa fa-check"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">启用中</span>
                            <span class="info-box-number">{$stats.active}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <div class="info-box">
                        <span class="info-box-icon bg-red"><i class="fa fa-times"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">已停用</span>
                            <span class="info-box-number">{$stats.inactive}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box">
                <div class="box-header with-border">
                    <h3 class="box-title">招聘公告列表</h3>
                    <div class="box-tools pull-right">
                        <a href="{:U('recruitmentnotice/edit')}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> 新增公告
                        </a>
                    </div>
                </div>

                <!-- 搜索表单 -->
                <div class="box-body">
                    <form method="get" class="form-inline" style="margin-bottom: 15px;">
                        <div class="form-group">
                            <select name="kw" class="form-control">
                                <volist name="c_kw" id="v" key="k">
                                    <option value="{$k}" <if condition="$s_kw eq $k">selected</if>>{$v}</option>
                                </volist>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="text" name="val" value="{$s_val}" class="form-control" placeholder="搜索内容">
                        </div>
                        <div class="form-group">
                            <select name="status" class="form-control">
                                <option value="">全部状态</option>
                                <volist name="statusList" id="v" key="k">
                                    <option value="{$k}" <if condition="$s_status eq $k">selected</if>>{$v.text}</option>
                                </volist>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="text" name="time[start]" value="{$s_start}" class="form-control datetime" placeholder="开始时间">
                        </div>
                        <div class="form-group">
                            <input type="text" name="time[end]" value="{$s_end}" class="form-control datetime" placeholder="结束时间">
                        </div>
                        <button type="submit" class="btn btn-default">搜索</button>
                        <a href="{:U('recruitmentnotice/index')}" class="btn btn-default">重置</a>
                    </form>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>公告标题</th>
                                    <th>招聘单位</th>
                                    <th>关联岗位</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <volist name="list" id="vo">
                                    <tr>
                                        <td>{$vo.id}</td>
                                        <td>
                                            <strong>{$vo.title}</strong>
                                            <if condition="$vo.description">
                                                <br><small class="text-muted">{$vo.description|mb_substr=0,50,'utf-8'}...</small>
                                            </if>
                                        </td>
                                        <td>{$vo.company_name|default="未设置"}</td>
                                        <td>
                                            <span class="badge bg-blue">{$vo.post_count}个岗位</span>
                                            <if condition="$vo.posts">
                                                <br>
                                                <volist name="vo.posts" id="post" offset="0" length="3">
                                                    <small class="label label-default">{$post.job_name}</small>
                                                </volist>
                                                <if condition="count($vo.posts) gt 3">
                                                    <small class="text-muted">...</small>
                                                </if>
                                            </if>
                                        </td>
                                        <td>
                                            <span class="label label-{$statusList[$vo.status].style}">
                                                {$statusList[$vo.status].text}
                                            </span>
                                        </td>
                                        <td>{$vo.create_time|date="Y-m-d H:i",###}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{:U('recruitmentnotice/detail', array('id'=>$vo['id']))}" 
                                                   class="btn btn-xs btn-info" title="查看详情">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{:U('recruitmentnotice/edit', array('id'=>$vo['id']))}" 
                                                   class="btn btn-xs btn-primary" title="编辑">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <a href="{:U('recruitmentnotice/requirements', array('notice_id'=>$vo['id']))}" 
                                                   class="btn btn-xs btn-warning" title="配置要求">
                                                    <i class="fa fa-cogs"></i>
                                                </a>
                                                <a href="{:U('resumematch/index', array('notice_id'=>$vo['id']))}" 
                                                   class="btn btn-xs btn-success" title="查看匹配">
                                                    <i class="fa fa-search"></i>
                                                </a>
                                                <if condition="$vo.status eq 1">
                                                    <a href="{:U('recruitmentnotice/cgstat', array('id'=>$vo['id'], 'status'=>0))}" 
                                                       class="btn btn-xs btn-default" title="停用" 
                                                       onclick="return confirm('确定要停用此公告吗？')">
                                                        <i class="fa fa-pause"></i>
                                                    </a>
                                                <else/>
                                                    <a href="{:U('recruitmentnotice/cgstat', array('id'=>$vo['id'], 'status'=>1))}" 
                                                       class="btn btn-xs btn-success" title="启用">
                                                        <i class="fa fa-play"></i>
                                                    </a>
                                                </if>
                                                <a href="{:U('recruitmentnotice/del', array('id'=>$vo['id']))}" 
                                                   class="btn btn-xs btn-danger" title="删除" 
                                                   onclick="return confirm('确定要删除此公告吗？删除后相关数据将无法恢复！')">
                                                    <i class="fa fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </volist>
                                <if condition="empty($list)">
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">暂无数据</td>
                                    </tr>
                                </if>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="box-footer clearfix">
                        {$page}
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

<script>
$(function() {
    // 日期时间选择器
    $('.datetime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        autoclose: true,
        todayBtn: true,
        language: 'zh-CN'
    });
});
</script>

</body>
</html>
