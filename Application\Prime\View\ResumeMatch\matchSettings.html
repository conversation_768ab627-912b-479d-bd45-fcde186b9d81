<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简历匹配设置</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                简历匹配设置
                <small>{$notice.title}</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentnotice/index')}">招聘公告管理</a></li>
                <li><a href="{:U('resumematch/index', array('notice_id'=>$notice['id']))}">匹配结果</a></li>
                <li class="active">匹配设置</li>
            </ol>
        </section>

        <section class="content">
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">执行简历匹配</h3>
                        </div>

                        <form method="post" action="{:U('resumematch/executeMatch')}" id="matchForm">
                            <input type="hidden" name="notice_id" value="{$notice.id}">
                            
                            <div class="box-body">
                                <!-- 当前状态信息 -->
                                <div class="alert alert-info">
                                    <h4><i class="fa fa-info-circle"></i> 当前状态</h4>
                                    <ul class="list-unstyled">
                                        <li><strong>招聘公告：</strong>{$notice.title}</li>
                                        <li><strong>现有匹配记录：</strong>{$existingCount} 条</li>
                                        <li><strong>可匹配简历：</strong>{$totalResumes} 份（沟通中和培训中的简历）</li>
                                    </ul>
                                </div>

                                <!-- 匹配选项 -->
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="clear_old" value="1" 
                                               <if condition="$existingCount gt 0">checked</if>> 
                                        清除现有匹配记录
                                    </label>
                                    <p class="help-block">
                                        <if condition="$existingCount gt 0">
                                            建议勾选此选项，以确保使用最新的筛选要求重新匹配
                                        <else/>
                                            当前没有匹配记录，可以直接执行匹配
                                        </if>
                                    </p>
                                </div>

                                <!-- 匹配范围选择 -->
                                <div class="form-group">
                                    <label>匹配范围：</label>
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="match_scope" value="all" checked>
                                            匹配所有符合条件的简历
                                        </label>
                                    </div>
                                    <div class="radio">
                                        <label>
                                            <input type="radio" name="match_scope" value="selected">
                                            仅匹配指定简历
                                        </label>
                                    </div>
                                </div>

                                <!-- 指定简历选择（默认隐藏） -->
                                <div class="form-group" id="resumeSelection" style="display: none;">
                                    <label>选择简历：</label>
                                    <div class="well" style="max-height: 200px; overflow-y: auto;">
                                        <div class="form-group">
                                            <input type="text" id="resumeSearch" class="form-control" 
                                                   placeholder="搜索简历（姓名、电话）...">
                                        </div>
                                        <div id="resumeList">
                                            <!-- 这里通过AJAX加载简历列表 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 匹配算法说明 -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#algorithmInfo">
                                                匹配算法说明 <i class="fa fa-chevron-down"></i>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="algorithmInfo" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <p>系统将根据以下条件进行匹配，总分100分：</p>
                                            <ul>
                                                <li><strong>年龄匹配（15分）：</strong>简历年龄在岗位要求范围内</li>
                                                <li><strong>性别匹配（10分）：</strong>简历性别符合岗位要求</li>
                                                <li><strong>身高匹配（10分）：</strong>简历身高在岗位要求范围内</li>
                                                <li><strong>学历匹配（25分）：</strong>简历学历达到岗位要求，超出要求可加分</li>
                                                <li><strong>专业匹配（20分）：</strong>简历专业包含岗位要求的关键词</li>
                                                <li><strong>工作经验（15分）：</strong>简历工作年限符合岗位要求</li>
                                                <li><strong>其他条件（5分）：</strong>健康状况、恐高等其他要求</li>
                                            </ul>
                                            <p class="text-muted">
                                                <small>
                                                    注：年龄、性别、身高、学历为硬性条件，不符合将标记为"不符合要求"；
                                                    其他条件为软性条件，不符合不影响"符合要求"状态，但会影响匹配分数。
                                                </small>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 预计处理时间提示 -->
                                <div class="alert alert-warning">
                                    <i class="fa fa-clock-o"></i>
                                    <strong>预计处理时间：</strong>
                                    根据简历数量，匹配过程可能需要几秒到几分钟不等，请耐心等待。
                                </div>
                            </div>

                            <div class="box-footer">
                                <button type="submit" class="btn btn-primary btn-lg" id="executeBtn">
                                    <i class="fa fa-play"></i> 开始匹配
                                </button>
                                <a href="{:U('resumematch/index', array('notice_id'=>$notice['id']))}" 
                                   class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> 返回
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

<script>
$(function() {
    // 匹配范围选择
    $('input[name="match_scope"]').change(function() {
        if ($(this).val() === 'selected') {
            $('#resumeSelection').show();
            loadResumeList();
        } else {
            $('#resumeSelection').hide();
        }
    });

    // 加载简历列表
    function loadResumeList() {
        $.get('{:U("resumematch/getResumeList")}', function(data) {
            if (data.status === 1) {
                var html = '';
                $.each(data.data, function(i, resume) {
                    html += '<label class="checkbox resume-item" data-name="' + resume.name + resume.phone + '">' +
                           '<input type="checkbox" name="resume_ids[]" value="' + resume.id + '">' +
                           '<strong>' + resume.name + '</strong> ' +
                           '<small class="text-muted">(' + resume.phone + ')</small>' +
                           '</label>';
                });
                $('#resumeList').html(html);
            }
        });
    }

    // 简历搜索
    $('#resumeSearch').on('input', function() {
        var keyword = $(this).val().toLowerCase();
        $('.resume-item').each(function() {
            var name = $(this).data('name').toLowerCase();
            if (name.indexOf(keyword) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // 表单提交处理
    $('#matchForm').submit(function() {
        var scope = $('input[name="match_scope"]:checked').val();
        if (scope === 'selected') {
            var selectedResumes = $('input[name="resume_ids[]"]:checked').length;
            if (selectedResumes === 0) {
                alert('请选择要匹配的简历');
                return false;
            }
        }

        // 显示加载状态
        $('#executeBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 匹配中...');
        
        return true;
    });

    // 全选/反选功能
    $('#resumeList').before('<label class="checkbox"><input type="checkbox" id="selectAllResumes"> 全选/反选</label>');
    
    $(document).on('change', '#selectAllResumes', function() {
        var checked = $(this).is(':checked');
        $('.resume-item:visible input[type="checkbox"]').prop('checked', checked);
    });
});
</script>

</body>
</html>
