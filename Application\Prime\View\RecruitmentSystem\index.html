<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>招聘公告智能筛选系统</title>
    <include file="Public/header" />
    <style>
    .system-nav { margin-bottom: 20px; }
    .system-nav .nav-tabs { border-bottom: 2px solid #3c8dbc; }
    .system-nav .nav-tabs > li.active > a { background-color: #3c8dbc; color: white; border-color: #3c8dbc; }
    .tab-content { min-height: 500px; }
    .notice-card { border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin-bottom: 15px; }
    .notice-card:hover { box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .match-badge { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: white; }
    .match-excellent { background-color: #28a745; }
    .match-good { background-color: #007bff; }
    .match-fair { background-color: #ffc107; color: #333; }
    .match-poor { background-color: #dc3545; }
    </style>
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                招聘公告智能筛选系统
                <small>统一管理平台</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li class="active">招聘筛选系统</li>
            </ol>
        </section>

        <section class="content">
            <!-- 系统导航 -->
            <div class="system-nav">
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active">
                        <a href="#notices" role="tab" data-toggle="tab">
                            <i class="fa fa-file-text"></i> 招聘公告管理
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#matches" role="tab" data-toggle="tab">
                            <i class="fa fa-search"></i> 匹配结果查看
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#settings" role="tab" data-toggle="tab">
                            <i class="fa fa-cogs"></i> 系统设置
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 选项卡内容 -->
            <div class="tab-content">
                <!-- 招聘公告管理 -->
                <div role="tabpanel" class="tab-pane active" id="notices">
                    <!-- 统计信息 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="info-box">
                                <span class="info-box-icon bg-aqua"><i class="fa fa-file-text"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总公告数</span>
                                    <span class="info-box-number">{$stats.total}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="info-box">
                                <span class="info-box-icon bg-green"><i class="fa fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">启用中</span>
                                    <span class="info-box-number">{$stats.active}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="info-box">
                                <span class="info-box-icon bg-red"><i class="fa fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已停用</span>
                                    <span class="info-box-number">{$stats.inactive}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="info-box">
                                <span class="info-box-icon bg-yellow"><i class="fa fa-plus"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">快速操作</span>
                                    <span class="info-box-number">
                                        <a href="{:U('recruitmentsystem/edit')}" class="btn btn-primary btn-xs">
                                            新增公告
                                        </a>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索表单 -->
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">招聘公告列表</h3>
                        </div>
                        <div class="box-body">
                            <form method="get" class="form-inline" style="margin-bottom: 15px;">
                                <div class="form-group">
                                    <select name="kw" class="form-control">
                                        <volist name="c_kw" id="v" key="k">
                                            <option value="{$k}" <if condition="$s_kw eq $k">selected</if>>{$v}</option>
                                        </volist>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <input type="text" name="val" value="{$s_val}" class="form-control" placeholder="搜索内容">
                                </div>
                                <div class="form-group">
                                    <select name="status" class="form-control">
                                        <option value="">全部状态</option>
                                        <volist name="statusList" id="v" key="k">
                                            <option value="{$k}" <if condition="$s_status eq $k">selected</if>>{$v.text}</option>
                                        </volist>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-default">搜索</button>
                                <a href="{:U('recruitmentsystem/index')}" class="btn btn-default">重置</a>
                            </form>

                            <!-- 公告列表 -->
                            <div class="row">
                                <volist name="list" id="vo">
                                    <div class="col-md-6">
                                        <div class="notice-card">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h4>
                                                        {$vo.title}
                                                        <span class="label label-{$statusList[$vo.status].style}">
                                                            {$statusList[$vo.status].text}
                                                        </span>
                                                    </h4>
                                                    <p class="text-muted">
                                                        <if condition="$vo.company_name">
                                                            <i class="fa fa-building"></i> {$vo.company_name}<br>
                                                        </if>
                                                        <i class="fa fa-calendar"></i> {$vo.create_time|date="Y-m-d",###}<br>
                                                        <i class="fa fa-briefcase"></i> 关联 {$vo.post_count} 个岗位
                                                    </p>
                                                    <if condition="$vo.posts">
                                                        <div>
                                                            <volist name="vo.posts" id="post" offset="0" length="3">
                                                                <span class="label label-default">{$post.job_name}</span>
                                                            </volist>
                                                            <if condition="count($vo.posts) gt 3">
                                                                <span class="text-muted">...</span>
                                                            </if>
                                                        </div>
                                                    </if>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <div class="btn-group-vertical">
                                                        <a href="{:U('recruitmentsystem/edit', array('id'=>$vo['id']))}" 
                                                           class="btn btn-primary btn-sm">
                                                            <i class="fa fa-edit"></i> 编辑
                                                        </a>
                                                        <a href="{:U('recruitmentsystem/requirements', array('notice_id'=>$vo['id']))}" 
                                                           class="btn btn-warning btn-sm">
                                                            <i class="fa fa-cogs"></i> 配置要求
                                                        </a>
                                                        <a href="{:U('recruitmentsystem/matches', array('notice_id'=>$vo['id']))}" 
                                                           class="btn btn-success btn-sm">
                                                            <i class="fa fa-search"></i> 查看匹配
                                                        </a>
                                                        <if condition="$vo.status eq 1">
                                                            <a href="{:U('recruitmentsystem/cgstat', array('id'=>$vo['id'], 'status'=>0))}" 
                                                               class="btn btn-default btn-sm" 
                                                               onclick="return confirm('确定要停用此公告吗？')">
                                                                <i class="fa fa-pause"></i> 停用
                                                            </a>
                                                        <else/>
                                                            <a href="{:U('recruitmentsystem/cgstat', array('id'=>$vo['id'], 'status'=>1))}" 
                                                               class="btn btn-success btn-sm">
                                                                <i class="fa fa-play"></i> 启用
                                                            </a>
                                                        </if>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </volist>
                                <if condition="empty($list)">
                                    <div class="col-md-12">
                                        <div class="text-center text-muted">
                                            <i class="fa fa-exclamation-triangle fa-3x"></i>
                                            <h4>暂无招聘公告</h4>
                                            <p>点击右上角"新增公告"开始创建</p>
                                        </div>
                                    </div>
                                </if>
                            </div>

                            <!-- 分页 -->
                            <div class="text-center">
                                {$page}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 匹配结果查看 -->
                <div role="tabpanel" class="tab-pane" id="matches">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">选择招聘公告查看匹配结果</h3>
                        </div>
                        <div class="box-body">
                            <div id="matchNoticeList">
                                <p class="text-center text-muted">
                                    <i class="fa fa-spinner fa-spin"></i> 加载中...
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div role="tabpanel" class="tab-pane" id="settings">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">系统设置</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>匹配算法说明</h4>
                                    <table class="table table-bordered">
                                        <tr><td>年龄匹配</td><td>15分</td><td>硬性条件</td></tr>
                                        <tr><td>性别匹配</td><td>10分</td><td>硬性条件</td></tr>
                                        <tr><td>身高匹配</td><td>10分</td><td>硬性条件</td></tr>
                                        <tr><td>学历匹配</td><td>25分</td><td>硬性条件</td></tr>
                                        <tr><td>专业匹配</td><td>20分</td><td>软性条件</td></tr>
                                        <tr><td>工作经验</td><td>15分</td><td>软性条件</td></tr>
                                        <tr><td>其他条件</td><td>5分</td><td>软性条件</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h4>系统信息</h4>
                                    <table class="table table-bordered">
                                        <tr><td>系统版本</td><td>v1.0.0</td></tr>
                                        <tr><td>开发框架</td><td>ThinkPHP 3.x</td></tr>
                                        <tr><td>数据库</td><td>MySQL</td></tr>
                                        <tr><td>更新时间</td><td>2025-08-14</td></tr>
                                    </table>
                                    
                                    <h4>快速操作</h4>
                                    <div class="btn-group-vertical btn-block">
                                        <a href="{:U('recruitmentsystem/edit')}" class="btn btn-primary">
                                            <i class="fa fa-plus"></i> 创建新公告
                                        </a>
                                        <a href="{:U('recruitmentsystem/matches')}" class="btn btn-success">
                                            <i class="fa fa-search"></i> 查看所有匹配结果
                                        </a>
                                        <button type="button" class="btn btn-info" onclick="clearAllMatches()">
                                            <i class="fa fa-refresh"></i> 清除所有匹配记录
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

<script>
$(function() {
    // 加载匹配结果选择列表
    $('a[href="#matches"]').on('shown.bs.tab', function() {
        loadMatchNotices();
    });

    function loadMatchNotices() {
        $.get('{:U("recruitmentsystem/matches")}', function(data) {
            $('#matchNoticeList').html(data);
        });
    }
});

function clearAllMatches() {
    if (confirm('确定要清除所有匹配记录吗？此操作不可恢复！')) {
        $.post('{:U("recruitmentsystem/clearAllMatches")}', function(result) {
            if (result.status == 1) {
                alert('清除成功');
                location.reload();
            } else {
                alert('清除失败：' + result.info);
            }
        });
    }
}
</script>

</body>
</html>
