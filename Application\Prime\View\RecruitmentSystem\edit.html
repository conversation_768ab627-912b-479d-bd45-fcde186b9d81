<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><if condition="isset($row)">编辑<else/>新增</if>招聘公告</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                <if condition="isset($row)">编辑<else/>新增</if>招聘公告
                <small>智能筛选系统</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentsystem/index')}">招聘筛选系统</a></li>
                <li class="active"><if condition="isset($row)">编辑<else/>新增</if>公告</li>
            </ol>
        </section>

        <section class="content">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">公告信息</h3>
                </div>

                <form method="post" class="form-horizontal">
                    <if condition="isset($row)">
                        <input type="hidden" name="id" value="{$row.id}">
                    </if>
                    
                    <div class="box-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">公告标题 <span class="text-red">*</span></label>
                            <div class="col-sm-8">
                                <input type="text" name="title" value="{$row.title|default=''}" 
                                       class="form-control" placeholder="请输入公告标题" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">招聘单位</label>
                            <div class="col-sm-8">
                                <input type="text" name="company_name" value="{$row.company_name|default=''}" 
                                       class="form-control" placeholder="请输入招聘单位名称">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">公告描述</label>
                            <div class="col-sm-8">
                                <textarea name="description" class="form-control" rows="4" 
                                          placeholder="请输入公告描述">{$row.description|default=''}</textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态</label>
                            <div class="col-sm-8">
                                <volist name="statusList" id="v" key="k">
                                    <label class="radio-inline">
                                        <input type="radio" name="status" value="{$k}" 
                                               <if condition="(isset($row) && $row['status'] eq $k) || (!isset($row) && $k eq 1)">checked</if>>
                                        {$v.text}
                                    </label>
                                </volist>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">关联岗位</label>
                            <div class="col-sm-8">
                                <div class="well" style="max-height: 300px; overflow-y: auto;">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <input type="text" id="postSearch" class="form-control" 
                                                       placeholder="搜索岗位名称...">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <if condition="empty($availablePosts)">
                                        <p class="text-muted">暂无可用岗位</p>
                                    <else/>
                                        <div id="postList">
                                            <volist name="availablePosts" id="post">
                                                <div class="post-item" data-name="{$post.job_name} {$post.project_name}">
                                                    <label class="checkbox">
                                                        <input type="checkbox" name="post_ids[]" value="{$post.id}"
                                                               <if condition="isset($relatedPostIds) && in_array($post['id'], $relatedPostIds)">checked</if>>
                                                        <strong>{$post.job_name}</strong>
                                                        <small class="text-muted">({$post.project_name})</small>
                                                    </label>
                                                </div>
                                            </volist>
                                        </div>
                                    </if>
                                </div>
                                <small class="help-block">选择要关联到此公告的岗位，可多选</small>
                            </div>
                        </div>
                    </div>

                    <div class="box-footer">
                        <div class="col-sm-offset-2 col-sm-8">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> 保存
                            </button>
                            <a href="{:U('recruitmentsystem/index')}" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> 返回
                            </a>
                            <if condition="isset($row)">
                                <a href="{:U('recruitmentsystem/requirements', array('notice_id'=>$row['id']))}" 
                                   class="btn btn-warning">
                                    <i class="fa fa-cogs"></i> 配置岗位要求
                                </a>
                            </if>
                        </div>
                    </div>
                </form>
            </div>
        </section>
    </div>
</div>

<include file="Public/footer" />

<script>
$(function() {
    // 岗位搜索功能
    $('#postSearch').on('input', function() {
        var keyword = $(this).val().toLowerCase();
        $('.post-item').each(function() {
            var name = $(this).data('name').toLowerCase();
            if (name.indexOf(keyword) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // 全选/反选功能
    $('#postList').before('<div class="form-group"><label class="checkbox"><input type="checkbox" id="selectAll"> 全选/反选</label></div>');
    
    $('#selectAll').change(function() {
        var checked = $(this).is(':checked');
        $('.post-item:visible input[type="checkbox"]').prop('checked', checked);
    });

    // 表单验证
    $('form').submit(function() {
        var title = $('input[name="title"]').val().trim();
        if (!title) {
            alert('请填写公告标题');
            $('input[name="title"]').focus();
            return false;
        }
        
        return true;
    });
});
</script>

</body>
</html>
