<?php
namespace Common\Model;

use Think\Model;

/**
 * 岗位筛选要求模型
 */
class PostRequirementsModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 性别要求定义
     */
    public $gender = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '男', 'style' => 'primary'],
        '2' => ['text' => '女', 'style' => 'warning'],
    ];

    /**
     * 学历要求定义
     */
    public $education_level = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '中专及以上', 'style' => 'info'],
        '2' => ['text' => '大专及以上', 'style' => 'info'],
        '3' => ['text' => '本科及以上', 'style' => 'info'],
        '4' => ['text' => '研究生及以上', 'style' => 'info'],
        '5' => ['text' => '硕士', 'style' => 'info'],
    ];

    /**
     * 恐高要求定义
     */
    public $is_afraid_heights = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '不能恐高', 'style' => 'danger'],
        '2' => ['text' => '可以恐高', 'style' => 'success'],
    ];

    /**
     * 婚姻状况要求定义
     */
    public $marital_status = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '未婚', 'style' => 'primary'],
        '2' => ['text' => '已婚', 'style' => 'success'],
    ];

    /**
     * 保存岗位要求
     * @param array $data 要求数据
     * @return bool|int
     */
    public function saveRequirements($data)
    {
        // 检查是否已存在该岗位的要求
        $existing = $this->where([
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();

        if ($existing) {
            // 更新现有记录
            $data['id'] = $existing['id'];
            return $this->save($data);
        } else {
            // 新增记录
            return $this->add($data);
        }
    }

    /**
     * 获取岗位要求详情
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @return array|false
     */
    public function getRequirements($postId, $noticeId)
    {
        return $this->where([
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->find();
    }

    /**
     * 批量设置多个岗位的相同要求
     * @param array $postIds 岗位ID数组
     * @param int $noticeId 公告ID
     * @param array $requirements 要求数据
     * @return bool
     */
    public function batchSetRequirements($postIds, $noticeId, $requirements)
    {
        $this->startTrans();
        try {
            foreach ($postIds as $postId) {
                $data = $requirements;
                $data['post_id'] = $postId;
                $data['notice_id'] = $noticeId;
                
                $this->saveRequirements($data);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 删除岗位要求
     * @param int $postId 岗位ID
     * @param int $noticeId 公告ID
     * @return bool
     */
    public function deleteRequirements($postId, $noticeId)
    {
        return $this->where([
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->delete();
    }

    /**
     * 获取要求的可读文本描述
     * @param array $requirements 要求数据
     * @return string
     */
    public function getRequirementsText($requirements)
    {
        $text = [];

        // 年龄要求
        if ($requirements['min_age'] || $requirements['max_age']) {
            $ageText = '年龄：';
            if ($requirements['min_age'] && $requirements['max_age']) {
                $ageText .= $requirements['min_age'] . '-' . $requirements['max_age'] . '岁';
            } elseif ($requirements['min_age']) {
                $ageText .= $requirements['min_age'] . '岁以上';
            } elseif ($requirements['max_age']) {
                $ageText .= $requirements['max_age'] . '岁以下';
            }
            $text[] = $ageText;
        }

        // 性别要求
        if ($requirements['gender'] > 0) {
            $text[] = '性别：' . $this->gender[$requirements['gender']]['text'];
        }

        // 身高要求
        if ($requirements['min_height'] || $requirements['max_height']) {
            $heightText = '身高：';
            if ($requirements['min_height'] && $requirements['max_height']) {
                $heightText .= $requirements['min_height'] . '-' . $requirements['max_height'] . 'cm';
            } elseif ($requirements['min_height']) {
                $heightText .= $requirements['min_height'] . 'cm以上';
            } elseif ($requirements['max_height']) {
                $heightText .= $requirements['max_height'] . 'cm以下';
            }
            $text[] = $heightText;
        }

        // 学历要求
        if ($requirements['education_level'] > 0) {
            $text[] = '学历：' . $this->education_level[$requirements['education_level']]['text'];
        }

        // 专业要求
        if ($requirements['major_keywords']) {
            $text[] = '专业：' . $requirements['major_keywords'];
        }

        // 工作经验
        if ($requirements['min_work_years'] || $requirements['max_work_years']) {
            $workText = '工作经验：';
            if ($requirements['min_work_years'] && $requirements['max_work_years']) {
                $workText .= $requirements['min_work_years'] . '-' . $requirements['max_work_years'] . '年';
            } elseif ($requirements['min_work_years']) {
                $workText .= $requirements['min_work_years'] . '年以上';
            } elseif ($requirements['max_work_years']) {
                $workText .= $requirements['max_work_years'] . '年以下';
            }
            $text[] = $workText;
        }

        // 其他要求
        if ($requirements['health_status']) {
            $text[] = '健康状况：' . $requirements['health_status'];
        }

        if ($requirements['is_afraid_heights'] > 0) {
            $text[] = '恐高：' . $this->is_afraid_heights[$requirements['is_afraid_heights']]['text'];
        }

        if ($requirements['political_status']) {
            $text[] = '政治面貌：' . $requirements['political_status'];
        }

        if ($requirements['marital_status'] > 0) {
            $text[] = '婚姻状况：' . $this->marital_status[$requirements['marital_status']]['text'];
        }

        return implode('；', $text);
    }
}
