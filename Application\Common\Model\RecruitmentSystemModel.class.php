<?php
namespace Common\Model;

use Think\Model;

/**
 * 招聘筛选系统统一模型
 * 整合所有招聘相关的数据操作
 */
class RecruitmentSystemModel extends Model
{
    // 不绑定具体表，使用M()函数操作不同表
    protected $autoCheckFields = false;

    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
        ['update_time', 'time', self::MODEL_UPDATE, 'function'],
    ];

    /**
     * 状态定义
     */
    public $status = [
        '0' => ['text' => '停用', 'style' => 'danger'],
        '1' => ['text' => '启用', 'style' => 'success'],
    ];

    public $gender = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '男', 'style' => 'primary'],
        '2' => ['text' => '女', 'style' => 'warning'],
    ];

    public $education_level = [
        '0' => ['text' => '不限', 'style' => 'info'],
        '1' => ['text' => '中专及以上', 'style' => 'info'],
        '2' => ['text' => '大专及以上', 'style' => 'info'],
        '3' => ['text' => '本科及以上', 'style' => 'info'],
        '4' => ['text' => '研究生及以上', 'style' => 'info'],
        '5' => ['text' => '硕士', 'style' => 'info'],
    ];

    public $is_qualified = [
        '0' => ['text' => '不符合', 'style' => 'danger'],
        '1' => ['text' => '符合', 'style' => 'success'],
    ];

    // ==================== 招聘公告相关方法 ====================

    /**
     * 获取招聘公告列表
     */
    public function getNoticeList($where = [], $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $list = M('recruitment_notice')->where($where)
            ->order('id DESC')
            ->limit($offset, $limit)
            ->select();

        if ($list) {
            $noticeIds = array_column($list, 'id');
            $postRelations = M('recruitment_notice_post')
                ->alias('rnp')
                ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
                ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
                ->where(['rnp.notice_id' => ['in', $noticeIds]])
                ->field('rnp.notice_id, pp.id as post_id, pp.job_name, p.name as project_name')
                ->select();

            $postsByNotice = [];
            foreach ($postRelations as $relation) {
                $postsByNotice[$relation['notice_id']][] = $relation;
            }

            foreach ($list as &$notice) {
                $notice['posts'] = isset($postsByNotice[$notice['id']]) ? $postsByNotice[$notice['id']] : [];
                $notice['post_count'] = count($notice['posts']);
            }
        }

        return $list;
    }

    /**
     * 获取招聘公告详情
     */
    public function getNoticeDetail($id)
    {
        $notice = M('recruitment_notice')->find($id);
        if (!$notice) return false;

        $posts = M('recruitment_notice_post')
            ->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $id])
            ->field('pp.*, p.name as project_name, rnp.id as relation_id')
            ->select();

        if ($posts) {
            $postIds = array_column($posts, 'id');
            $requirements = M('post_requirements')
                ->where(['post_id' => ['in', $postIds], 'notice_id' => $id])
                ->select();

            $requirementsByPost = [];
            foreach ($requirements as $req) {
                $requirementsByPost[$req['post_id']] = $req;
            }

            foreach ($posts as &$post) {
                $post['requirements'] = isset($requirementsByPost[$post['id']]) ? $requirementsByPost[$post['id']] : null;
            }
        }

        $notice['posts'] = $posts ?: [];
        return $notice;
    }

    /**
     * 保存招聘公告
     */
    public function saveNotice($data, $postIds = [])
    {
        M()->startTrans();
        try {
            if (isset($data['id']) && $data['id']) {
                // 更新
                $noticeId = $data['id'];
                M('recruitment_notice')->save($data);
            } else {
                // 新增
                $noticeId = M('recruitment_notice')->add($data);
            }

            // 处理岗位关联
            if (!empty($postIds)) {
                M('recruitment_notice_post')->where(['notice_id' => $noticeId])->delete();
                foreach ($postIds as $postId) {
                    M('recruitment_notice_post')->add([
                        'notice_id' => $noticeId,
                        'post_id' => $postId,
                        'create_time' => time()
                    ]);
                }
            }

            M()->commit();
            return $noticeId;
        } catch (\Exception $e) {
            M()->rollback();
            return false;
        }
    }

    /**
     * 删除招聘公告
     */
    public function deleteNotice($id)
    {
        M()->startTrans();
        try {
            M('recruitment_notice_post')->where(['notice_id' => $id])->delete();
            M('post_requirements')->where(['notice_id' => $id])->delete();
            M('resume_post_match')->where(['notice_id' => $id])->delete();
            M('recruitment_notice')->delete($id);
            
            M()->commit();
            return true;
        } catch (\Exception $e) {
            M()->rollback();
            return false;
        }
    }

    // ==================== 岗位要求相关方法 ====================

    /**
     * 保存岗位要求
     */
    public function saveRequirements($data)
    {
        $existing = M('post_requirements')->where([
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();

        if ($existing) {
            $data['id'] = $existing['id'];
            return M('post_requirements')->save($data);
        } else {
            return M('post_requirements')->add($data);
        }
    }

    /**
     * 获取岗位要求
     */
    public function getRequirements($postId, $noticeId)
    {
        return M('post_requirements')->where([
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->find();
    }

    /**
     * 批量设置岗位要求
     */
    public function batchSetRequirements($postIds, $noticeId, $requirements)
    {
        M()->startTrans();
        try {
            foreach ($postIds as $postId) {
                $data = $requirements;
                $data['post_id'] = $postId;
                $data['notice_id'] = $noticeId;
                $this->saveRequirements($data);
            }
            M()->commit();
            return true;
        } catch (\Exception $e) {
            M()->rollback();
            return false;
        }
    }

    // ==================== 匹配结果相关方法 ====================

    /**
     * 保存匹配结果
     */
    public function saveMatch($data)
    {
        $existing = M('resume_post_match')->where([
            'user_job_id' => $data['user_job_id'],
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();

        if ($existing) {
            $data['id'] = $existing['id'];
            return M('resume_post_match')->save($data);
        } else {
            return M('resume_post_match')->add($data);
        }
    }

    /**
     * 获取匹配结果列表
     */
    public function getMatchResults($noticeId, $filters = [], $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        $where = ['rpm.notice_id' => $noticeId];

        if (isset($filters['is_qualified']) && $filters['is_qualified'] !== '') {
            $where['rpm.is_qualified'] = $filters['is_qualified'];
        }
        if (isset($filters['min_score']) && $filters['min_score'] !== '') {
            $where['rpm.match_score'] = ['egt', $filters['min_score']];
        }
        if (isset($filters['post_id']) && $filters['post_id']) {
            $where['rpm.post_id'] = $filters['post_id'];
        }

        return M('resume_post_match')
            ->alias('rpm')
            ->join('LEFT JOIN __USER_JOB__ uj ON rpm.user_job_id = uj.id')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where($where)
            ->field('rpm.*, uj.name, uj.phone, uj.gender, uj.age, uj.education_level, uj.major, pp.job_name, p.name as project_name')
            ->order('rpm.match_score DESC, rpm.id DESC')
            ->limit($offset, $limit)
            ->select();
    }

    /**
     * 获取匹配统计
     */
    public function getMatchStats($noticeId)
    {
        $stats = [];
        $stats['total'] = M('resume_post_match')->where(['notice_id' => $noticeId])->count();
        $stats['qualified'] = M('resume_post_match')->where(['notice_id' => $noticeId, 'is_qualified' => 1])->count();
        $stats['unqualified'] = $stats['total'] - $stats['qualified'];
        
        $avgScore = M('resume_post_match')->where(['notice_id' => $noticeId])->avg('match_score');
        $stats['avg_score'] = round($avgScore, 2);

        $stats['score_ranges'] = [
            'excellent' => M('resume_post_match')->where(['notice_id' => $noticeId, 'match_score' => ['egt', 90]])->count(),
            'good' => M('resume_post_match')->where(['notice_id' => $noticeId, 'match_score' => ['between', [80, 89]]])->count(),
            'fair' => M('resume_post_match')->where(['notice_id' => $noticeId, 'match_score' => ['between', [60, 79]]])->count(),
            'poor' => M('resume_post_match')->where(['notice_id' => $noticeId, 'match_score' => ['lt', 60]])->count(),
        ];

        return $stats;
    }

    /**
     * 获取可用岗位列表
     */
    public function getAvailablePosts()
    {
        return M('project_post')
            ->alias('pp')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['pp.status' => 1, 'p.status' => 1])
            ->field('pp.id, pp.job_name, p.name as project_name')
            ->order('p.id ASC, pp.id ASC')
            ->select();
    }

    /**
     * 获取简历的匹配岗位
     */
    public function getResumeMatches($userJobIds)
    {
        if (empty($userJobIds)) return [];

        $matches = M('resume_post_match')
            ->alias('rpm')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rpm.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON rpm.notice_id = rn.id')
            ->where(['rpm.user_job_id' => ['in', $userJobIds], 'rpm.is_qualified' => 1])
            ->field('rpm.user_job_id, rpm.match_score, pp.job_name, p.name as project_name, rn.title as notice_title')
            ->order('rpm.match_score DESC')
            ->select();

        $result = [];
        foreach ($matches as $match) {
            $result[$match['user_job_id']][] = $match;
        }
        return $result;
    }
}
