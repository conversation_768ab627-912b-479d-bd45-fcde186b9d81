<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 招聘公告管理控制器
 */
class RecruitmentNoticeController extends PrimeController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 招聘公告列表
     */
    public function index()
    {
        $c_kw = [
            'title' => '公告标题',
            'company_name' => '招聘单位',
            'id' => 'ID',
        ];

        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);

        // 状态筛选
        if ($s_status !== '') $where['status'] = $s_status;

        // 基础搜索条件
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['title', 'company_name'])) {
                $where[$s_kw] = ['like', "%$s_val%"];
            } else {
                $where[$s_kw] = $s_val;
            }
        }

        // 时间筛选
        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("RecruitmentNotice");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 20);
        
        $list = $obj->getNoticeList($where, $page->nowPage, $page->listRows);

        // 获取统计信息
        $stats = [
            'total' => $obj->count(),
            'active' => $obj->where(['status' => 1])->count(),
            'inactive' => $obj->where(['status' => 0])->count(),
        ];

        $this->assign('list', $list);
        $this->assign("page", $page->show());
        $this->assign('stats', $stats);
        $this->assign('statusList', $obj->status);
        $this->assign('c_kw', $c_kw);
        $this->assign('s_kw', $s_kw);
        $this->assign('s_val', $s_val);
        $this->assign('s_status', $s_status);
        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '');
        $this->assign('_get', I('get.'));
        $this->display();
    }

    /**
     * 添加/编辑招聘公告
     */
    public function edit()
    {
        $id = intval(I('get.id'));
        $obj = D("RecruitmentNotice");
        
        if ($id) {
            $row = $obj->getNoticeDetail($id);
            if (!$row) $this->error('参数错误');
            $this->assign('row', $row);
            
            // 获取已关联的岗位ID
            $relatedPostIds = D('RecruitmentNoticePost')->getNoticePostIds($id);
            $this->assign('relatedPostIds', $relatedPostIds);
        }

        if (IS_POST) {
            $data = I('post.');
            
            // 验证必填字段
            if (empty($data['title'])) {
                $this->error('请填写公告标题');
            }

            $obj->startTrans();
            try {
                if (!$id) {
                    // 新增
                    $data['create_time'] = time();
                    $noticeId = $obj->add($data);
                    if (!$noticeId) {
                        throw new \Exception('添加公告失败');
                    }
                } else {
                    // 更新
                    $data['id'] = $id;
                    $data['update_time'] = time();
                    $result = $obj->save($data);
                    if ($result === false) {
                        throw new \Exception('更新公告失败');
                    }
                    $noticeId = $id;
                }

                // 处理岗位关联
                $postIds = I('post.post_ids', []);
                if (!empty($postIds)) {
                    $relationResult = D('RecruitmentNoticePost')->addPostRelations($noticeId, $postIds);
                    if (!$relationResult) {
                        throw new \Exception('关联岗位失败');
                    }
                }

                $obj->commit();
                $this->success("操作成功", U("recruitmentnotice/index"));
            } catch (\Exception $e) {
                $obj->rollback();
                $this->error($e->getMessage());
            }
        }

        // 获取可用岗位列表
        $availablePosts = $obj->getAvailablePosts();
        $this->assign('availablePosts', $availablePosts);
        $this->assign('statusList', $obj->status);
        $this->display();
    }

    /**
     * 查看招聘公告详情
     */
    public function detail()
    {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        $obj = D("RecruitmentNotice");
        $notice = $obj->getNoticeDetail($id);
        if (!$notice) $this->error('公告不存在');

        // 获取匹配统计信息
        $matchStats = D('ResumePostMatch')->getMatchStats($id);

        $this->assign('notice', $notice);
        $this->assign('matchStats', $matchStats);
        $this->assign('statusList', $obj->status);
        $this->display();
    }

    /**
     * 删除招聘公告
     */
    public function del()
    {
        $id = I('get.id/d', 0);
        if ($id <= 0) {
            $this->error('参数错误');
        }

        $obj = D("RecruitmentNotice");
        
        // 检查是否存在
        $row = $obj->find($id);
        if (!$row) {
            $this->error('记录不存在');
        }

        // 检查是否有匹配记录
        $matchCount = D('ResumePostMatch')->where(['notice_id' => $id])->count();
        if ($matchCount > 0) {
            $this->error('该公告已有匹配记录，无法删除');
        }

        $result = $obj->deleteNotice($id);
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 状态变更
     */
    public function cgstat()
    {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        
        $obj = D("RecruitmentNotice");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误');

        $result = $obj->where("id=".$id)->save(['status' => $status, 'update_time' => time()]);
        if ($result !== false) {
            $this->success('操作成功');
        } else {
            $this->error('操作失败');
        }
    }

    /**
     * 配置岗位要求
     */
    public function requirements()
    {
        $noticeId = intval(I('get.notice_id'));
        $postId = intval(I('get.post_id'));
        
        if (!$noticeId) $this->error('参数错误');

        $notice = D("RecruitmentNotice")->find($noticeId);
        if (!$notice) $this->error('公告不存在');

        // 获取公告关联的岗位
        $posts = D('RecruitmentNoticePost')->getNoticePosts($noticeId);
        if (empty($posts)) $this->error('该公告尚未关联任何岗位');

        // 如果指定了岗位ID，获取该岗位的要求
        $currentPost = null;
        $requirements = null;
        if ($postId) {
            foreach ($posts as $post) {
                if ($post['id'] == $postId) {
                    $currentPost = $post;
                    break;
                }
            }
            if (!$currentPost) $this->error('岗位不存在或未关联到该公告');
            
            $requirements = D('PostRequirements')->getRequirements($postId, $noticeId);
        }

        if (IS_POST) {
            $data = I('post.');
            $data['notice_id'] = $noticeId;
            
            $requirementsObj = D('PostRequirements');
            
            if (isset($data['batch_mode']) && $data['batch_mode'] == 1) {
                // 批量设置模式
                $selectedPostIds = I('post.selected_post_ids', []);
                if (empty($selectedPostIds)) {
                    $this->error('请选择要设置的岗位');
                }
                
                $result = $requirementsObj->batchSetRequirements($selectedPostIds, $noticeId, $data);
            } else {
                // 单个岗位设置模式
                if (!$postId) $this->error('参数错误');
                $data['post_id'] = $postId;
                $result = $requirementsObj->saveRequirements($data);
            }

            if ($result) {
                $this->success("设置成功", U("recruitmentnotice/requirements", ['notice_id' => $noticeId]));
            } else {
                $this->error('设置失败');
            }
        }

        $this->assign('notice', $notice);
        $this->assign('posts', $posts);
        $this->assign('currentPost', $currentPost);
        $this->assign('requirements', $requirements);
        $this->assign('postId', $postId);
        
        // 获取选项列表
        $requirementsObj = D('PostRequirements');
        $this->assign('genderList', $requirementsObj->gender);
        $this->assign('educationList', $requirementsObj->education_level);
        $this->assign('afraidHeightsList', $requirementsObj->is_afraid_heights);
        $this->assign('maritalStatusList', $requirementsObj->marital_status);
        
        $this->display();
    }
}
