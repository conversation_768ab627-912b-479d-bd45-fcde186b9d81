<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>配置岗位要求</title>
    <include file="Public/header" />
</head>
<body>
<div class="wrapper">
    <include file="Public/nav" />
    
    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                配置岗位要求
                <small>{$notice.title}</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{:U('index/index')}"><i class="fa fa-dashboard"></i> 首页</a></li>
                <li><a href="{:U('recruitmentnotice/index')}">招聘公告管理</a></li>
                <li class="active">配置岗位要求</li>
            </ol>
        </section>

        <section class="content">
            <div class="row">
                <!-- 左侧岗位列表 -->
                <div class="col-md-4">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">关联岗位</h3>
                        </div>
                        <div class="box-body">
                            <if condition="empty($posts)">
                                <p class="text-muted">该公告尚未关联任何岗位</p>
                                <a href="{:U('recruitmentnotice/edit', array('id'=>$notice['id']))}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fa fa-plus"></i> 关联岗位
                                </a>
                            <else/>
                                <div class="list-group">
                                    <volist name="posts" id="post">
                                        <a href="{:U('recruitmentnotice/requirements', array('notice_id'=>$notice['id'], 'post_id'=>$post['id']))}" 
                                           class="list-group-item <if condition='$postId eq $post.id'>active</if>">
                                            <h5 class="list-group-item-heading">{$post.job_name}</h5>
                                            <p class="list-group-item-text">
                                                <small>{$post.project_name}</small>
                                                <if condition="$post.requirements">
                                                    <br><span class="label label-success">已配置</span>
                                                <else/>
                                                    <br><span class="label label-warning">未配置</span>
                                                </if>
                                            </p>
                                        </a>
                                    </volist>
                                </div>
                                
                                <!-- 批量设置按钮 -->
                                <div class="text-center" style="margin-top: 15px;">
                                    <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#batchModal">
                                        <i class="fa fa-copy"></i> 批量设置
                                    </button>
                                </div>
                            </if>
                        </div>
                    </div>
                </div>

                <!-- 右侧要求配置 -->
                <div class="col-md-8">
                    <if condition="$currentPost">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    {$currentPost.job_name} - 筛选要求配置
                                </h3>
                            </div>

                            <form method="post" class="form-horizontal">
                                <div class="box-body">
                                    <!-- 基本条件 -->
                                    <div class="form-group">
                                        <label class="col-sm-12 control-label">
                                            <h4><i class="fa fa-user"></i> 基本条件</h4>
                                        </label>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">年龄要求</label>
                                        <div class="col-sm-4">
                                            <input type="number" name="min_age" value="{$requirements.min_age|default=''}" 
                                                   class="form-control" placeholder="最小年龄" min="16" max="65">
                                        </div>
                                        <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                        <div class="col-sm-4">
                                            <input type="number" name="max_age" value="{$requirements.max_age|default=''}" 
                                                   class="form-control" placeholder="最大年龄" min="16" max="65">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">性别要求</label>
                                        <div class="col-sm-9">
                                            <volist name="genderList" id="v" key="k">
                                                <label class="radio-inline">
                                                    <input type="radio" name="gender" value="{$k}" 
                                                           <if condition="(isset($requirements) && $requirements['gender'] eq $k) || (!isset($requirements) && $k eq 0)">checked</if>>
                                                    {$v.text}
                                                </label>
                                            </volist>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">身高要求</label>
                                        <div class="col-sm-4">
                                            <input type="number" name="min_height" value="{$requirements.min_height|default=''}" 
                                                   class="form-control" placeholder="最小身高(cm)" min="140" max="200">
                                        </div>
                                        <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                        <div class="col-sm-4">
                                            <input type="number" name="max_height" value="{$requirements.max_height|default=''}" 
                                                   class="form-control" placeholder="最大身高(cm)" min="140" max="200">
                                        </div>
                                    </div>

                                    <!-- 学历要求 -->
                                    <div class="form-group">
                                        <label class="col-sm-12 control-label">
                                            <h4><i class="fa fa-graduation-cap"></i> 学历要求</h4>
                                        </label>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">学历水平</label>
                                        <div class="col-sm-9">
                                            <select name="education_level" class="form-control">
                                                <volist name="educationList" id="v" key="k">
                                                    <option value="{$k}" <if condition="isset($requirements) && $requirements['education_level'] eq $k">selected</if>>
                                                        {$v.text}
                                                    </option>
                                                </volist>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">专业关键词</label>
                                        <div class="col-sm-9">
                                            <input type="text" name="major_keywords" value="{$requirements.major_keywords|default=''}" 
                                                   class="form-control" placeholder="多个关键词用逗号分隔，如：计算机,软件工程,信息技术">
                                            <small class="help-block">简历专业包含任一关键词即可匹配</small>
                                        </div>
                                    </div>

                                    <!-- 工作经验 -->
                                    <div class="form-group">
                                        <label class="col-sm-12 control-label">
                                            <h4><i class="fa fa-briefcase"></i> 工作经验</h4>
                                        </label>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">工作年限</label>
                                        <div class="col-sm-4">
                                            <input type="number" name="min_work_years" value="{$requirements.min_work_years|default=''}" 
                                                   class="form-control" placeholder="最少年限" min="0" max="30">
                                        </div>
                                        <div class="col-sm-1 text-center" style="padding-top: 7px;">至</div>
                                        <div class="col-sm-4">
                                            <input type="number" name="max_work_years" value="{$requirements.max_work_years|default=''}" 
                                                   class="form-control" placeholder="最多年限" min="0" max="30">
                                        </div>
                                    </div>

                                    <!-- 其他要求 -->
                                    <div class="form-group">
                                        <label class="col-sm-12 control-label">
                                            <h4><i class="fa fa-cogs"></i> 其他要求</h4>
                                        </label>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">健康状况</label>
                                        <div class="col-sm-9">
                                            <input type="text" name="health_status" value="{$requirements.health_status|default=''}" 
                                                   class="form-control" placeholder="如：健康，无传染病等">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">恐高要求</label>
                                        <div class="col-sm-9">
                                            <volist name="afraidHeightsList" id="v" key="k">
                                                <label class="radio-inline">
                                                    <input type="radio" name="is_afraid_heights" value="{$k}" 
                                                           <if condition="(isset($requirements) && $requirements['is_afraid_heights'] eq $k) || (!isset($requirements) && $k eq 0)">checked</if>>
                                                    {$v.text}
                                                </label>
                                            </volist>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">政治面貌</label>
                                        <div class="col-sm-9">
                                            <input type="text" name="political_status" value="{$requirements.political_status|default=''}" 
                                                   class="form-control" placeholder="如：党员，团员，群众等">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">婚姻状况</label>
                                        <div class="col-sm-9">
                                            <volist name="maritalStatusList" id="v" key="k">
                                                <label class="radio-inline">
                                                    <input type="radio" name="marital_status" value="{$k}" 
                                                           <if condition="(isset($requirements) && $requirements['marital_status'] eq $k) || (!isset($requirements) && $k eq 0)">checked</if>>
                                                    {$v.text}
                                                </label>
                                            </volist>
                                        </div>
                                    </div>
                                </div>

                                <div class="box-footer">
                                    <div class="col-sm-offset-3 col-sm-9">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fa fa-save"></i> 保存配置
                                        </button>
                                        <a href="{:U('recruitmentnotice/index')}" class="btn btn-default">
                                            <i class="fa fa-arrow-left"></i> 返回列表
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    <else/>
                        <div class="box">
                            <div class="box-body text-center">
                                <i class="fa fa-arrow-left fa-3x text-muted"></i>
                                <h4 class="text-muted">请选择左侧岗位进行配置</h4>
                            </div>
                        </div>
                    </if>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- 批量设置模态框 -->
<div class="modal fade" id="batchModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">批量设置岗位要求</h4>
            </div>
            <form method="post" id="batchForm">
                <input type="hidden" name="batch_mode" value="1">
                <div class="modal-body">
                    <div class="form-group">
                        <label>选择岗位：</label>
                        <div class="well" style="max-height: 200px; overflow-y: auto;">
                            <volist name="posts" id="post">
                                <label class="checkbox">
                                    <input type="checkbox" name="selected_post_ids[]" value="{$post.id}">
                                    <strong>{$post.job_name}</strong> <small>({$post.project_name})</small>
                                </label>
                            </volist>
                        </div>
                    </div>

                    <!-- 批量设置的要求配置（简化版） -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>年龄要求：</label>
                                <div class="row">
                                    <div class="col-xs-6">
                                        <input type="number" name="min_age" class="form-control" placeholder="最小年龄">
                                    </div>
                                    <div class="col-xs-6">
                                        <input type="number" name="max_age" class="form-control" placeholder="最大年龄">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>性别要求：</label>
                                <select name="gender" class="form-control">
                                    <volist name="genderList" id="v" key="k">
                                        <option value="{$k}">{$v.text}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>身高要求：</label>
                                <div class="row">
                                    <div class="col-xs-6">
                                        <input type="number" name="min_height" class="form-control" placeholder="最小身高">
                                    </div>
                                    <div class="col-xs-6">
                                        <input type="number" name="max_height" class="form-control" placeholder="最大身高">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>学历要求：</label>
                                <select name="education_level" class="form-control">
                                    <volist name="educationList" id="v" key="k">
                                        <option value="{$k}">{$v.text}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>专业关键词：</label>
                        <input type="text" name="major_keywords" class="form-control"
                               placeholder="多个关键词用逗号分隔">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>工作年限：</label>
                                <div class="row">
                                    <div class="col-xs-6">
                                        <input type="number" name="min_work_years" class="form-control" placeholder="最少年限">
                                    </div>
                                    <div class="col-xs-6">
                                        <input type="number" name="max_work_years" class="form-control" placeholder="最多年限">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>恐高要求：</label>
                                <select name="is_afraid_heights" class="form-control">
                                    <volist name="afraidHeightsList" id="v" key="k">
                                        <option value="{$k}">{$v.text}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">批量设置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<include file="Public/footer" />

<script>
$(function() {
    // 批量设置表单验证
    $('#batchForm').submit(function() {
        var selectedPosts = $('input[name="selected_post_ids[]"]:checked').length;
        if (selectedPosts === 0) {
            alert('请至少选择一个岗位');
            return false;
        }
        return true;
    });

    // 全选/反选功能
    $('#batchModal').find('.well').before(
        '<label class="checkbox"><input type="checkbox" id="selectAllPosts"> 全选/反选</label>'
    );

    $('#selectAllPosts').change(function() {
        var checked = $(this).is(':checked');
        $('input[name="selected_post_ids[]"]').prop('checked', checked);
    });

    // 表单验证提示
    $('form:not(#batchForm)').submit(function() {
        var hasValue = false;
        $(this).find('input, select').each(function() {
            if ($(this).val() && $(this).attr('name') !== 'post_id' && $(this).attr('name') !== 'notice_id') {
                hasValue = true;
                return false;
            }
        });

        if (!hasValue) {
            return confirm('未设置任何筛选条件，确定要保存吗？');
        }

        return true;
    });
});
</script>

</body>
</html>
