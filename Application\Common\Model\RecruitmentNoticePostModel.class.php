<?php
namespace Common\Model;

use Think\Model;

/**
 * 招聘公告岗位关联模型
 */
class RecruitmentNoticePostModel extends Model
{
    protected $_auto = [
        ['create_time', 'time', self::MODEL_INSERT, 'function'],
    ];

    /**
     * 添加岗位关联
     * @param int $noticeId 公告ID
     * @param array $postIds 岗位ID数组
     * @return bool
     */
    public function addPostRelations($noticeId, $postIds)
    {
        if (empty($postIds)) {
            return true;
        }

        $this->startTrans();
        try {
            // 先删除现有关联
            $this->where(['notice_id' => $noticeId])->delete();

            // 添加新关联
            foreach ($postIds as $postId) {
                $this->add([
                    'notice_id' => $noticeId,
                    'post_id' => $postId,
                    'create_time' => time()
                ]);
            }

            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 获取公告关联的岗位ID列表
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getNoticePostIds($noticeId)
    {
        $relations = $this->where(['notice_id' => $noticeId])->getField('post_id', true);
        return $relations ?: [];
    }

    /**
     * 获取公告关联的岗位详情
     * @param int $noticeId 公告ID
     * @return array
     */
    public function getNoticePosts($noticeId)
    {
        return $this->alias('rnp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON rnp.post_id = pp.id')
            ->join('LEFT JOIN __PROJECT__ p ON pp.project_id = p.id')
            ->where(['rnp.notice_id' => $noticeId])
            ->field('pp.*, p.name as project_name, rnp.id as relation_id')
            ->order('p.id ASC, pp.id ASC')
            ->select();
    }

    /**
     * 删除岗位关联
     * @param int $noticeId 公告ID
     * @param int $postId 岗位ID（可选，不传则删除所有关联）
     * @return bool
     */
    public function deletePostRelation($noticeId, $postId = null)
    {
        $where = ['notice_id' => $noticeId];
        if ($postId) {
            $where['post_id'] = $postId;
        }

        return $this->where($where)->delete();
    }

    /**
     * 检查岗位是否已关联到公告
     * @param int $noticeId 公告ID
     * @param int $postId 岗位ID
     * @return bool
     */
    public function isPostRelated($noticeId, $postId)
    {
        $count = $this->where([
            'notice_id' => $noticeId,
            'post_id' => $postId
        ])->count();

        return $count > 0;
    }

    /**
     * 获取岗位关联的公告列表
     * @param int $postId 岗位ID
     * @return array
     */
    public function getPostNotices($postId)
    {
        return $this->alias('rnp')
            ->join('LEFT JOIN __RECRUITMENT_NOTICE__ rn ON rnp.notice_id = rn.id')
            ->where(['rnp.post_id' => $postId, 'rn.status' => 1])
            ->field('rn.*, rnp.id as relation_id')
            ->order('rn.id DESC')
            ->select();
    }
}
